import Link from 'next/link'
import {
  Clock,
  Shield,
  MapPin,
  Smartphone,
  Users,
  ArrowRight,
  CheckCircle,
  Star
} from 'lucide-react'

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Fast & Reliable
              <span className="block text-blue-200">Local Delivery</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Book local parcel deliveries with real-time tracking, estimated delivery times,
              and secure user accounts. Your packages delivered safely, on time.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/book"
                className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center"
              >
                Book Delivery Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link
                href="/track"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center"
              >
                Track Package
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose HyperLocal Delivery?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We provide the fastest, most reliable local delivery service with cutting-edge technology
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Real-Time Tracking</h3>
              <p className="text-gray-600">
                Track your packages in real-time with live updates and estimated delivery times
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Secure & Safe</h3>
              <p className="text-gray-600">
                Your packages are handled with care by verified delivery partners
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Hyperlocal Coverage</h3>
              <p className="text-gray-600">
                Fast delivery within your city with optimized routes and local partners
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Simple steps to get your package delivered
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">Book Online</h3>
              <p className="text-gray-600">Fill out our simple booking form with pickup and delivery details</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">Partner Assigned</h3>
              <p className="text-gray-600">We assign the nearest verified delivery partner to your order</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">Real-time Updates</h3>
              <p className="text-gray-600">Track your package with live updates and notifications</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-xl font-semibold mb-2">Safe Delivery</h3>
              <p className="text-gray-600">Your package is delivered safely to the destination</p>
            </div>
          </div>
        </div>
      </section>

      {/* Mobile App Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Smartphone className="h-16 w-16 text-blue-600 mx-auto mb-6" />
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Download Our Mobile App
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Get the HyperLocal Delivery app for even faster booking and tracking on the go
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#"
                className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors inline-flex items-center"
              >
                <span className="mr-2">📱</span>
                Download for iOS
              </a>
              <a
                href="#"
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors inline-flex items-center"
              >
                <span className="mr-2">🤖</span>
                Download for Android
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Delivery Partner CTA */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Users className="h-16 w-16 mx-auto mb-6" />
            <h2 className="text-4xl font-bold mb-4">
              Become a Delivery Partner
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join our network of verified delivery partners and start earning with flexible schedules
            </p>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center justify-center">
                <CheckCircle className="h-6 w-6 mr-2" />
                <span>Flexible Working Hours</span>
              </div>
              <div className="flex items-center justify-center">
                <CheckCircle className="h-6 w-6 mr-2" />
                <span>Competitive Earnings</span>
              </div>
              <div className="flex items-center justify-center">
                <CheckCircle className="h-6 w-6 mr-2" />
                <span>Weekly Payments</span>
              </div>
            </div>
            <Link
              href="/partner/register"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center"
            >
              Join as Partner
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.&quot;
              </p>
              <div className="font-semibold">Priya Sharma</div>
              <div className="text-gray-500 text-sm">Mumbai</div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.&quot;
              </p>
              <div className="font-semibold">Rajesh Kumar</div>
              <div className="text-gray-500 text-sm">Delhi</div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.&quot;
              </p>
              <div className="font-semibold">Amit Singh</div>
              <div className="text-gray-500 text-sm">Delivery Partner</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
