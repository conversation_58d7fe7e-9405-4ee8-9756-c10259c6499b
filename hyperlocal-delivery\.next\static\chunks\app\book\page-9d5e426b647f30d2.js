(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[774],{486:(e,s,r)=>{Promise.resolve().then(r.bind(r,7576))},3168:(e,s,r)=>{"use strict";r.d(s,{UU:()=>d});var t=r(67),a=r(9509);let n=a.env.NEXT_PUBLIC_SUPABASE_URL,i=a.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function d(){return(0,t.createBrowserClient)(n,i)}},3999:(e,s,r)=>{"use strict";function t(){let e=Date.now().toString(36).toUpperCase(),s=Math.random().toString(36).substring(2,6).toUpperCase();return"".concat("HLD").concat(e).concat(s)}function a(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function n(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function i(e,s,r,t){let a=Math.PI/180*(r-e),n=Math.PI/180*(t-s),i=Math.sin(a/2)*Math.sin(a/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*r)*Math.sin(n/2)*Math.sin(n/2);return 2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i))*6371}function d(e){let s=new Date;return s.setMinutes(s.getMinutes()+(30+5*e)),s}function l(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function c(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}r.d(s,{Iw:()=>c,Q8:()=>d,Yq:()=>n,gd:()=>i,qX:()=>t,qY:()=>l,vv:()=>a})},4186:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7108:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7576:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(5155),a=r(2115),n=r(5695),i=r(3168),d=r(3999),l=r(4516),c=r(7108),o=r(4186);let u=(0,r(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);function m(){let[e,s]=(0,a.useState)(null),[r,m]=(0,a.useState)([]),[g,p]=(0,a.useState)({pickupAddressId:"",deliveryAddressId:"",packageDescription:"",packageWeight:"",packageDimensions:"",deliveryInstructions:"",urgency:"standard"}),[x,h]=(0,a.useState)(0),[y,b]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),[k,j]=(0,a.useState)(""),N=(0,n.useRouter)(),w=(0,i.UU)();(0,a.useEffect)(()=>{(async()=>{let{data:{user:e}}=await w.auth.getUser();if(!e)return N.push("/auth/login");s(e);let{data:r}=await w.from("addresses").select("*").eq("user_id",e.id).order("is_default",{ascending:!1});m(r||[])})()},[w,N]),(0,a.useEffect)(()=>{if(g.pickupAddressId&&g.deliveryAddressId){let e=r.find(e=>e.id===g.pickupAddressId),s=r.find(e=>e.id===g.deliveryAddressId);if((null==e?void 0:e.latitude)&&(null==e?void 0:e.longitude)&&(null==s?void 0:s.latitude)&&(null==s?void 0:s.longitude)){let r=(0,d.gd)(e.latitude,e.longitude,s.latitude,s.longitude);h(Math.round((50+10*r)*("express"===g.urgency?1.5:1)*(g.packageWeight?Math.max(1,parseFloat(g.packageWeight)/5):1)));let t=(0,d.Q8)(r);"express"===g.urgency&&t.setMinutes(t.getMinutes()-15),b(t)}}},[g,r]);let _=e=>{p({...g,[e.target.name]:e.target.value})},A=async s=>{s.preventDefault(),v(!0),j("");try{let s=(0,d.qX)(),{data:r,error:t}=await w.from("orders").insert({customer_id:e.id,tracking_id:s,pickup_address_id:g.pickupAddressId,delivery_address_id:g.deliveryAddressId,package_description:g.packageDescription,package_weight:g.packageWeight?parseFloat(g.packageWeight):null,package_dimensions:g.packageDimensions||null,delivery_instructions:g.deliveryInstructions||null,estimated_delivery_time:null==y?void 0:y.toISOString(),total_amount:x,status:"pending"}).select().single();if(t)return void j(t.message);await w.from("tracking_events").insert({order_id:r.id,event_type:"order_placed",description:"Order placed successfully",timestamp:new Date().toISOString()}),N.push("/track?id=".concat(s))}catch(e){j("Failed to create order. Please try again.")}finally{v(!1)}};return e?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"container mx-auto px-4 max-w-4xl",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Book a Delivery"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Fill in the details below to book your delivery"})]}),(0,t.jsxs)("form",{onSubmit:A,className:"space-y-8",children:[k&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:k}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(l.A,{className:"inline h-4 w-4 mr-1"}),"Pickup Address"]}),(0,t.jsxs)("select",{name:"pickupAddressId",value:g.pickupAddressId,onChange:_,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select pickup address"}),r.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.label," - ",e.address_line_1,", ",e.city]},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(l.A,{className:"inline h-4 w-4 mr-1"}),"Delivery Address"]}),(0,t.jsxs)("select",{name:"deliveryAddressId",value:g.deliveryAddressId,onChange:_,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select delivery address"}),r.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.label," - ",e.address_line_1,", ",e.city]},e.id))]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Package Details"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Package Description *"}),(0,t.jsx)("textarea",{name:"packageDescription",value:g.packageDescription,onChange:_,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe what you're sending..."})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Weight (kg)"}),(0,t.jsx)("input",{type:"number",name:"packageWeight",value:g.packageWeight,onChange:_,step:"0.1",min:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 2.5"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Dimensions (L x W x H cm)"}),(0,t.jsx)("input",{type:"text",name:"packageDimensions",value:g.packageDimensions,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 30 x 20 x 10"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delivery Instructions"}),(0,t.jsx)("textarea",{name:"deliveryInstructions",value:g.deliveryInstructions,onChange:_,rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Any special instructions for delivery..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(o.A,{className:"inline h-4 w-4 mr-1"}),"Delivery Urgency"]}),(0,t.jsxs)("select",{name:"urgency",value:g.urgency,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"standard",children:"Standard (Normal delivery)"}),(0,t.jsx)("option",{value:"express",children:"Express (+50% cost, faster delivery)"})]})]})]}),x>0&&(0,t.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(u,{className:"h-5 w-5 mr-2"}),"Cost Summary"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Estimated Cost:"}),(0,t.jsxs)("span",{className:"font-semibold",children:["₹",x]})]}),y&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Estimated Delivery:"}),(0,t.jsx)("span",{className:"font-semibold",children:y.toLocaleString("en-IN",{hour:"2-digit",minute:"2-digit",day:"numeric",month:"short"})})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:f||!g.pickupAddressId||!g.deliveryAddressId,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Booking...":"Book Delivery - ₹".concat(x)})})]})]})})}):(0,t.jsx)("div",{children:"Loading..."})}}},e=>{var s=s=>e(e.s=s);e.O(0,[214,441,684,358],()=>s(486)),_N_E=e.O()}]);