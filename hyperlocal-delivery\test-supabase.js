// Simple Supabase connection test
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://ehcjrmnvcyfvokukdtlr.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVoY2pybW52Y3lmdm9rdWtkdGxyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyODQ4NjUsImV4cCI6MjA2NDg2MDg2NX0.N2kI5uYhxmha95xSiwsKgKauMp2yq0R64ddYWi5udKQ'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  try {
    console.log('Testing Supabase connection...')
    
    // Test basic connection
    const { data, error } = await supabase.from('users').select('count').limit(1)
    
    if (error) {
      console.error('Connection test failed:', error.message)
      if (error.message.includes('relation "users" does not exist')) {
        console.log('\n❌ Database tables not found!')
        console.log('📋 You need to run the database setup script first.')
        console.log('👉 Go to Supabase SQL Editor and run the database-setup.sql script')
      }
    } else {
      console.log('✅ Supabase connection successful!')
      console.log('✅ Database tables exist!')
    }
  } catch (err) {
    console.error('❌ Connection failed:', err.message)
  }
}

testConnection()
