import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation/Navigation";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "HyperLocal Delivery - Fast & Reliable Local Delivery Service",
  description: "Book local parcel deliveries with real-time tracking, estimated delivery times, and secure user accounts. Join our delivery partner network today!",
  keywords: "local delivery, parcel delivery, hyperlocal, tracking, delivery service",
  authors: [{ name: "HyperLocal Delivery Team" }],
  openGraph: {
    title: "HyperLocal Delivery - Fast & Reliable Local Delivery Service",
    description: "Book local parcel deliveries with real-time tracking and estimated delivery times.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-gray-50`}>
        <Navigation />
        <main className="min-h-screen">
          {children}
        </main>
        <footer className="bg-gray-900 text-white py-8">
          <div className="container mx-auto px-4 text-center">
            <p>&copy; 2024 HyperLocal Delivery. All rights reserved.</p>
          </div>
        </footer>
      </body>
    </html>
  );
}
