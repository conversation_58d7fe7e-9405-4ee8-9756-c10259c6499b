[{"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\login\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\register\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\book\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\partner\\register\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\track\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\components\\navigation\\Navigation.tsx": "10", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\lib\\supabase.ts": "11", "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\lib\\utils.ts": "12"}, {"size": 13509, "mtime": 1749282609418, "results": "13", "hashOfConfig": "14"}, {"size": 7671, "mtime": 1749282418854, "results": "15", "hashOfConfig": "14"}, {"size": 9768, "mtime": 1749282443637, "results": "16", "hashOfConfig": "14"}, {"size": 12968, "mtime": 1749282477113, "results": "17", "hashOfConfig": "14"}, {"size": 12457, "mtime": 1749282543803, "results": "18", "hashOfConfig": "14"}, {"size": 1451, "mtime": 1749282314123, "results": "19", "hashOfConfig": "14"}, {"size": 11332, "mtime": 1749282392131, "results": "20", "hashOfConfig": "14"}, {"size": 10673, "mtime": 1749282572298, "results": "21", "hashOfConfig": "14"}, {"size": 12337, "mtime": 1749282511076, "results": "22", "hashOfConfig": "14"}, {"size": 7328, "mtime": 1749282338738, "results": "23", "hashOfConfig": "14"}, {"size": 7259, "mtime": 1749283232076, "results": "24", "hashOfConfig": "14"}, {"size": 2678, "mtime": 1749282288900, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1oiwqxi", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\admin\\page.tsx", ["62", "63", "64"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\login\\page.tsx", ["65"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\register\\page.tsx", ["66"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\book\\page.tsx", ["67", "68", "69", "70"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\dashboard\\page.tsx", ["71"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\page.tsx", ["72", "73", "74", "75", "76", "77", "78"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\partner\\register\\page.tsx", ["79", "80", "81", "82", "83", "84"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\track\\page.tsx", ["85", "86"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\components\\navigation\\Navigation.tsx", ["87"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\lib\\supabase.ts", ["88", "89"], [], "C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\lib\\utils.ts", [], [], {"ruleId": "90", "severity": 2, "message": "91", "line": 31, "column": 10, "nodeType": null, "messageId": "92", "endLine": 31, "endColumn": 14}, {"ruleId": "93", "severity": 2, "message": "94", "line": 31, "column": 36, "nodeType": "95", "messageId": "96", "endLine": 31, "endColumn": 39, "suggestions": "97"}, {"ruleId": "98", "severity": 1, "message": "99", "line": 72, "column": 6, "nodeType": "100", "endLine": 72, "endColumn": 24, "suggestions": "101"}, {"ruleId": "90", "severity": 2, "message": "102", "line": 50, "column": 14, "nodeType": null, "messageId": "92", "endLine": 50, "endColumn": 17}, {"ruleId": "90", "severity": 2, "message": "102", "line": 92, "column": 14, "nodeType": null, "messageId": "92", "endLine": 92, "endColumn": 17}, {"ruleId": "90", "severity": 2, "message": "103", "line": 7, "column": 46, "nodeType": null, "messageId": "92", "endLine": 7, "endColumn": 50}, {"ruleId": "93", "severity": 2, "message": "94", "line": 22, "column": 36, "nodeType": "95", "messageId": "96", "endLine": 22, "endColumn": 39, "suggestions": "104"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 78, "column": 13, "nodeType": "107", "messageId": "108", "endLine": 78, "endColumn": 21, "fix": "109"}, {"ruleId": "90", "severity": 2, "message": "102", "line": 146, "column": 14, "nodeType": null, "messageId": "92", "endLine": 146, "endColumn": 17}, {"ruleId": "93", "severity": 2, "message": "94", "line": 29, "column": 36, "nodeType": "95", "messageId": "96", "endLine": 29, "endColumn": 39, "suggestions": "110"}, {"ruleId": "90", "severity": 2, "message": "111", "line": 3, "column": 3, "nodeType": null, "messageId": "92", "endLine": 3, "endColumn": 10}, {"ruleId": "112", "severity": 2, "message": "113", "line": 226, "column": 17, "nodeType": "114", "messageId": "115", "suggestions": "116"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 226, "column": 153, "nodeType": "114", "messageId": "115", "suggestions": "117"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 239, "column": 17, "nodeType": "114", "messageId": "115", "suggestions": "118"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 239, "column": 130, "nodeType": "114", "messageId": "115", "suggestions": "119"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 252, "column": 17, "nodeType": "114", "messageId": "115", "suggestions": "120"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 252, "column": 137, "nodeType": "114", "messageId": "115", "suggestions": "121"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 9, "column": 36, "nodeType": "95", "messageId": "96", "endLine": 9, "endColumn": 39, "suggestions": "122"}, {"ruleId": "90", "severity": 2, "message": "123", "line": 64, "column": 15, "nodeType": null, "messageId": "92", "endLine": 64, "endColumn": 19}, {"ruleId": "90", "severity": 2, "message": "102", "line": 104, "column": 14, "nodeType": null, "messageId": "92", "endLine": 104, "endColumn": 17}, {"ruleId": "112", "severity": 2, "message": "124", "line": 123, "column": 15, "nodeType": "114", "messageId": "115", "suggestions": "125"}, {"ruleId": "112", "severity": 2, "message": "124", "line": 270, "column": 77, "nodeType": "114", "messageId": "115", "suggestions": "126"}, {"ruleId": "112", "severity": 2, "message": "124", "line": 271, "column": 56, "nodeType": "114", "messageId": "115", "suggestions": "127"}, {"ruleId": "98", "severity": 1, "message": "128", "line": 59, "column": 6, "nodeType": "100", "endLine": 59, "endColumn": 20, "suggestions": "129"}, {"ruleId": "90", "severity": 2, "message": "102", "line": 100, "column": 14, "nodeType": null, "messageId": "92", "endLine": 100, "endColumn": 17}, {"ruleId": "90", "severity": 2, "message": "130", "line": 12, "column": 3, "nodeType": null, "messageId": "92", "endLine": 12, "endColumn": 9}, {"ruleId": "93", "severity": 2, "message": "94", "line": 33, "column": 49, "nodeType": "95", "messageId": "96", "endLine": 33, "endColumn": 52, "suggestions": "131"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 36, "column": 37, "nodeType": "95", "messageId": "96", "endLine": 36, "endColumn": 40, "suggestions": "132"}, "@typescript-eslint/no-unused-vars", "'user' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["133", "134"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardStats'. Either include it or remove the dependency array.", "ArrayExpression", ["135"], "'err' is defined but never used.", "'User' is defined but never used.", ["136", "137"], "prefer-const", "'baseCost' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "138", "text": "139"}, ["140", "141"], "'Package' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["142", "143", "144", "145"], ["146", "147", "148", "149"], ["150", "151", "152", "153"], ["154", "155", "156", "157"], ["158", "159", "160", "161"], ["162", "163", "164", "165"], ["166", "167"], "'data' is assigned a value but never used.", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["168", "169", "170", "171"], ["172", "173", "174", "175"], ["176", "177", "178", "179"], "React Hook useEffect has a missing dependency: 'handleTrack'. Either include it or remove the dependency array.", ["180"], "'Search' is defined but never used.", ["181", "182"], ["183", "184"], {"messageId": "185", "fix": "186", "desc": "187"}, {"messageId": "188", "fix": "189", "desc": "190"}, {"desc": "191", "fix": "192"}, {"messageId": "185", "fix": "193", "desc": "187"}, {"messageId": "188", "fix": "194", "desc": "190"}, [2379, 2396], "const baseCost = 50", {"messageId": "185", "fix": "195", "desc": "187"}, {"messageId": "188", "fix": "196", "desc": "190"}, {"messageId": "197", "data": "198", "fix": "199", "desc": "200"}, {"messageId": "197", "data": "201", "fix": "202", "desc": "203"}, {"messageId": "197", "data": "204", "fix": "205", "desc": "206"}, {"messageId": "197", "data": "207", "fix": "208", "desc": "209"}, {"messageId": "197", "data": "210", "fix": "211", "desc": "200"}, {"messageId": "197", "data": "212", "fix": "213", "desc": "203"}, {"messageId": "197", "data": "214", "fix": "215", "desc": "206"}, {"messageId": "197", "data": "216", "fix": "217", "desc": "209"}, {"messageId": "197", "data": "218", "fix": "219", "desc": "200"}, {"messageId": "197", "data": "220", "fix": "221", "desc": "203"}, {"messageId": "197", "data": "222", "fix": "223", "desc": "206"}, {"messageId": "197", "data": "224", "fix": "225", "desc": "209"}, {"messageId": "197", "data": "226", "fix": "227", "desc": "200"}, {"messageId": "197", "data": "228", "fix": "229", "desc": "203"}, {"messageId": "197", "data": "230", "fix": "231", "desc": "206"}, {"messageId": "197", "data": "232", "fix": "233", "desc": "209"}, {"messageId": "197", "data": "234", "fix": "235", "desc": "200"}, {"messageId": "197", "data": "236", "fix": "237", "desc": "203"}, {"messageId": "197", "data": "238", "fix": "239", "desc": "206"}, {"messageId": "197", "data": "240", "fix": "241", "desc": "209"}, {"messageId": "197", "data": "242", "fix": "243", "desc": "200"}, {"messageId": "197", "data": "244", "fix": "245", "desc": "203"}, {"messageId": "197", "data": "246", "fix": "247", "desc": "206"}, {"messageId": "197", "data": "248", "fix": "249", "desc": "209"}, {"messageId": "185", "fix": "250", "desc": "187"}, {"messageId": "188", "fix": "251", "desc": "190"}, {"messageId": "197", "data": "252", "fix": "253", "desc": "254"}, {"messageId": "197", "data": "255", "fix": "256", "desc": "257"}, {"messageId": "197", "data": "258", "fix": "259", "desc": "260"}, {"messageId": "197", "data": "261", "fix": "262", "desc": "263"}, {"messageId": "197", "data": "264", "fix": "265", "desc": "254"}, {"messageId": "197", "data": "266", "fix": "267", "desc": "257"}, {"messageId": "197", "data": "268", "fix": "269", "desc": "260"}, {"messageId": "197", "data": "270", "fix": "271", "desc": "263"}, {"messageId": "197", "data": "272", "fix": "273", "desc": "254"}, {"messageId": "197", "data": "274", "fix": "275", "desc": "257"}, {"messageId": "197", "data": "276", "fix": "277", "desc": "260"}, {"messageId": "197", "data": "278", "fix": "279", "desc": "263"}, {"desc": "280", "fix": "281"}, {"messageId": "185", "fix": "282", "desc": "187"}, {"messageId": "188", "fix": "283", "desc": "190"}, {"messageId": "185", "fix": "284", "desc": "187"}, {"messageId": "188", "fix": "285", "desc": "190"}, "suggestUnknown", {"range": "286", "text": "287"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "288", "text": "289"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [supabase, router, fetchDashboardStats]", {"range": "290", "text": "291"}, {"range": "292", "text": "287"}, {"range": "293", "text": "289"}, {"range": "294", "text": "287"}, {"range": "295", "text": "289"}, "replaceWithAlt", {"alt": "296"}, {"range": "297", "text": "298"}, "Replace with `&quot;`.", {"alt": "299"}, {"range": "300", "text": "301"}, "Replace with `&ldquo;`.", {"alt": "302"}, {"range": "303", "text": "304"}, "Replace with `&#34;`.", {"alt": "305"}, {"range": "306", "text": "307"}, "Replace with `&rdquo;`.", {"alt": "296"}, {"range": "308", "text": "309"}, {"alt": "299"}, {"range": "310", "text": "311"}, {"alt": "302"}, {"range": "312", "text": "313"}, {"alt": "305"}, {"range": "314", "text": "315"}, {"alt": "296"}, {"range": "316", "text": "317"}, {"alt": "299"}, {"range": "318", "text": "319"}, {"alt": "302"}, {"range": "320", "text": "321"}, {"alt": "305"}, {"range": "322", "text": "323"}, {"alt": "296"}, {"range": "324", "text": "325"}, {"alt": "299"}, {"range": "326", "text": "327"}, {"alt": "302"}, {"range": "328", "text": "329"}, {"alt": "305"}, {"range": "330", "text": "331"}, {"alt": "296"}, {"range": "332", "text": "333"}, {"alt": "299"}, {"range": "334", "text": "335"}, {"alt": "302"}, {"range": "336", "text": "337"}, {"alt": "305"}, {"range": "338", "text": "339"}, {"alt": "296"}, {"range": "340", "text": "341"}, {"alt": "299"}, {"range": "342", "text": "343"}, {"alt": "302"}, {"range": "344", "text": "345"}, {"alt": "305"}, {"range": "346", "text": "347"}, {"range": "348", "text": "287"}, {"range": "349", "text": "289"}, {"alt": "350"}, {"range": "351", "text": "352"}, "Replace with `&apos;`.", {"alt": "353"}, {"range": "354", "text": "355"}, "Replace with `&lsquo;`.", {"alt": "356"}, {"range": "357", "text": "358"}, "Replace with `&#39;`.", {"alt": "359"}, {"range": "360", "text": "361"}, "Replace with `&rsquo;`.", {"alt": "350"}, {"range": "362", "text": "363"}, {"alt": "353"}, {"range": "364", "text": "365"}, {"alt": "356"}, {"range": "366", "text": "367"}, {"alt": "359"}, {"range": "368", "text": "369"}, {"alt": "350"}, {"range": "370", "text": "371"}, {"alt": "353"}, {"range": "372", "text": "373"}, {"alt": "356"}, {"range": "374", "text": "375"}, {"alt": "359"}, {"range": "376", "text": "377"}, "Update the dependencies array to be: [handleTrack, searchParams]", {"range": "378", "text": "379"}, {"range": "380", "text": "287"}, {"range": "381", "text": "289"}, {"range": "382", "text": "287"}, {"range": "383", "text": "289"}, [634, 637], "unknown", [634, 637], "never", [1588, 1606], "[supabase, router, fetchDashboardStats]", [588, 591], [588, 591], [746, 749], [746, 749], "&quot;", [9645, 9814], "\n                &quot;Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.\"\n              ", "&ldquo;", [9645, 9814], "\n                &ldquo;Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.\"\n              ", "&#34;", [9645, 9814], "\n                &#34;Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.\"\n              ", "&rdquo;", [9645, 9814], "\n                &rdquo;Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.\"\n              ", [9645, 9814], "\n                \"Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.&quot;\n              ", [9645, 9814], "\n                \"Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.&ldquo;\n              ", [9645, 9814], "\n                \"Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.&#34;\n              ", [9645, 9814], "\n                \"Super fast delivery! My package was delivered within 2 hours. The tracking was accurate and the delivery partner was very professional.&rdquo;\n              ", [10308, 10454], "\n                &quot;Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.\"\n              ", [10308, 10454], "\n                &ldquo;Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.\"\n              ", [10308, 10454], "\n                &#34;Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.\"\n              ", [10308, 10454], "\n                &rdquo;Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.\"\n              ", [10308, 10454], "\n                \"Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.&quot;\n              ", [10308, 10454], "\n                \"Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.&ldquo;\n              ", [10308, 10454], "\n                \"Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.&#34;\n              ", [10308, 10454], "\n                \"Excellent service! I use HyperLocal for all my urgent deliveries. The real-time tracking gives me peace of mind.&rdquo;\n              ", [10947, 11100], "\n                &quot;As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.\"\n              ", [10947, 11100], "\n                &ldquo;As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.\"\n              ", [10947, 11100], "\n                &#34;As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.\"\n              ", [10947, 11100], "\n                &rdquo;As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.\"\n              ", [10947, 11100], "\n                \"As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.&quot;\n              ", [10947, 11100], "\n                \"As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.&ldquo;\n              ", [10947, 11100], "\n                \"As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.&#34;\n              ", [10947, 11100], "\n                \"As a delivery partner, I love the flexibility and the earnings. The app is easy to use and payments are always on time.&rdquo;\n              ", [317, 320], [317, 320], "&apos;", [3380, 3539], "\n            Your delivery partner application has been submitted successfully. \n            We&apos;ll review it and get back to you within 24-48 hours.\n          ", "&lsquo;", [3380, 3539], "\n            Your delivery partner application has been submitted successfully. \n            We&lsquo;ll review it and get back to you within 24-48 hours.\n          ", "&#39;", [3380, 3539], "\n            Your delivery partner application has been submitted successfully. \n            We&#39;ll review it and get back to you within 24-48 hours.\n          ", "&rsquo;", [3380, 3539], "\n            Your delivery partner application has been submitted successfully. \n            We&rsquo;ll review it and get back to you within 24-48 hours.\n          ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We&apos;ll verify your documents \n                    and conduct a background check. You'll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We&lsquo;ll verify your documents \n                    and conduct a background check. You'll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We&#39;ll verify your documents \n                    and conduct a background check. You'll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We&rsquo;ll verify your documents \n                    and conduct a background check. You'll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We'll verify your documents \n                    and conduct a background check. You&apos;ll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We'll verify your documents \n                    and conduct a background check. You&lsquo;ll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We'll verify your documents \n                    and conduct a background check. You&#39;ll receive an email notification once approved.\n                  ", [9806, 10032], "\n                    Your application will be reviewed within 24-48 hours. We'll verify your documents \n                    and conduct a background check. You&rsquo;ll receive an email notification once approved.\n                  ", [1454, 1468], "[handleTrack, searchParams]", [981, 984], [981, 984], [1086, 1089], [1086, 1089]]