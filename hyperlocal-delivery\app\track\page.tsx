'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase'
import { getStatusColor, getStatusText, formatDate } from '@/lib/utils'
import { Search, Package, MapPin, Clock, CheckCircle, Truck, User } from 'lucide-react'

interface Order {
  id: string
  tracking_id: string
  status: string
  package_description: string
  total_amount: number
  estimated_delivery_time: string | null
  actual_delivery_time: string | null
  created_at: string
  pickup_address: {
    address_line_1: string
    city: string
    state: string
  }
  delivery_address: {
    address_line_1: string
    city: string
    state: string
  }
  delivery_partner?: {
    user: {
      full_name: string
      phone: string
    }
  }
}

interface TrackingEvent {
  id: string
  event_type: string
  description: string
  location: string | null
  timestamp: string
}

export default function TrackingPage() {
  const [trackingId, setTrackingId] = useState('')
  const [order, setOrder] = useState<Order | null>(null)
  const [trackingEvents, setTrackingEvents] = useState<TrackingEvent[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const searchParams = useSearchParams()
  const supabase = createClient()

  useEffect(() => {
    const id = searchParams.get('id')
    if (id) {
      setTrackingId(id)
      handleTrack(id)
    }
  }, [searchParams])

  const handleTrack = async (id?: string) => {
    const idToTrack = id || trackingId
    if (!idToTrack) return

    setLoading(true)
    setError('')

    try {
      // Fetch order details
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select(`
          *,
          pickup_address:addresses!pickup_address_id(address_line_1, city, state),
          delivery_address:addresses!delivery_address_id(address_line_1, city, state),
          delivery_partner:delivery_partners(
            user:users(full_name, phone)
          )
        `)
        .eq('tracking_id', idToTrack)
        .single()

      if (orderError || !orderData) {
        setError('Order not found. Please check your tracking ID.')
        setOrder(null)
        setTrackingEvents([])
        return
      }

      setOrder(orderData)

      // Fetch tracking events
      const { data: eventsData } = await supabase
        .from('tracking_events')
        .select('*')
        .eq('order_id', orderData.id)
        .order('timestamp', { ascending: false })

      setTrackingEvents(eventsData || [])
    } catch (err) {
      setError('Failed to fetch tracking information')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleTrack()
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-6 w-6" />
      case 'assigned':
        return <User className="h-6 w-6" />
      case 'picked_up':
        return <Package className="h-6 w-6" />
      case 'in_transit':
        return <Truck className="h-6 w-6" />
      case 'delivered':
        return <CheckCircle className="h-6 w-6" />
      default:
        return <Package className="h-6 w-6" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Track Your Package</h1>
          
          <form onSubmit={handleSubmit} className="flex gap-4">
            <div className="flex-1 relative">
              <input
                type="text"
                value={trackingId}
                onChange={(e) => setTrackingId(e.target.value)}
                placeholder="Enter your tracking ID (e.g., HLD123ABC456)"
                className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <Search className="h-5 w-5 text-gray-400 absolute left-4 top-4" />
            </div>
            <button
              type="submit"
              disabled={loading || !trackingId}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Tracking...' : 'Track'}
            </button>
          </form>

          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
              {error}
            </div>
          )}
        </div>

        {/* Order Details */}
        {order && (
          <div className="space-y-8">
            {/* Status Overview */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Order #{order.tracking_id}</h2>
                  <p className="text-gray-600">Placed on {formatDate(order.created_at)}</p>
                </div>
                <div className={`px-4 py-2 rounded-full text-sm font-semibold ${getStatusColor(order.status)}`}>
                  {getStatusText(order.status)}
                </div>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <MapPin className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Pickup Address</h3>
                    <p className="text-gray-600 text-sm">
                      {order.pickup_address.address_line_1}<br />
                      {order.pickup_address.city}, {order.pickup_address.state}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <MapPin className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Delivery Address</h3>
                    <p className="text-gray-600 text-sm">
                      {order.delivery_address.address_line_1}<br />
                      {order.delivery_address.city}, {order.delivery_address.state}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-purple-100 p-2 rounded-lg">
                    <Package className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Package Details</h3>
                    <p className="text-gray-600 text-sm">{order.package_description}</p>
                    <p className="text-gray-600 text-sm font-semibold">₹{order.total_amount}</p>
                  </div>
                </div>
              </div>

              {order.estimated_delivery_time && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <span className="font-semibold text-blue-900">
                      Estimated Delivery: {formatDate(order.estimated_delivery_time)}
                    </span>
                  </div>
                </div>
              )}

              {order.delivery_partner && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Truck className="h-5 w-5 text-gray-600" />
                    <div>
                      <span className="font-semibold text-gray-900">Delivery Partner: </span>
                      <span className="text-gray-700">{order.delivery_partner.user.full_name}</span>
                      <span className="text-gray-500 ml-2">({order.delivery_partner.user.phone})</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Tracking Timeline */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Tracking Timeline</h3>
              
              <div className="space-y-6">
                {trackingEvents.map((event, index) => (
                  <div key={event.id} className="flex items-start space-x-4">
                    <div className={`p-2 rounded-full ${index === 0 ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}>
                      {getStatusIcon(event.event_type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900">{event.description}</h4>
                        <span className="text-sm text-gray-500">
                          {formatDate(event.timestamp)}
                        </span>
                      </div>
                      {event.location && (
                        <p className="text-gray-600 text-sm mt-1">📍 {event.location}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {trackingEvents.length === 0 && (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No tracking events yet</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 mt-8">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Need Help?</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Contact Support</h4>
              <p className="text-gray-600 text-sm mb-2">
                If you have any questions about your delivery, our support team is here to help.
              </p>
              <div className="space-y-1 text-sm">
                <p>📞 Phone: +91 1234567890</p>
                <p>📧 Email: <EMAIL></p>
                <p>💬 Live Chat: Available 24/7</p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Delivery Status Guide</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <span>Pending: Order received, waiting for partner assignment</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span>Assigned: Delivery partner assigned</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                  <span>Picked Up: Package collected from pickup location</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
                  <span>In Transit: Package is on the way to destination</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Delivered: Package successfully delivered</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
