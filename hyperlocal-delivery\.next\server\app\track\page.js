(()=>{var e={};e.id=970,e.ids=[970],e.modules={744:(e,s,t)=>{Promise.resolve().then(t.bind(t,6809))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1416:(e,s,t)=>{Promise.resolve().then(t.bind(t,8051))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6241:(e,s,t)=>{"use strict";function r(){let e=Date.now().toString(36).toUpperCase(),s=Math.random().toString(36).substring(2,6).toUpperCase();return`HLD${e}${s}`}function a(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function i(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function l(e,s,t,r){let a=Math.PI/180*(t-e),i=Math.PI/180*(r-s),l=Math.sin(a/2)*Math.sin(a/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*t)*Math.sin(i/2)*Math.sin(i/2);return 2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l))*6371}function n(e){let s=new Date;return s.setMinutes(s.getMinutes()+(30+5*e)),s}function d(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function c(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}t.d(s,{Iw:()=>c,Q8:()=>n,Yq:()=>i,gd:()=>l,qX:()=>r,qY:()=>d,vv:()=>a})},6809:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\apd deliary\\\\hyperlocal-delivery\\\\app\\\\track\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\track\\page.tsx","default")},7910:e=>{"use strict";e.exports=require("stream")},7992:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8051:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(687),a=t(3210),i=t(6189),l=t(6396),n=t(6241),d=t(8730),c=t(8869),o=t(6699),u=t(8059),p=t(5336),x=t(9270),m=t(7992);function h(){let[e,s]=(0,a.useState)(""),[t,h]=(0,a.useState)(null),[g,y]=(0,a.useState)([]),[v,f]=(0,a.useState)(!1),[b,j]=(0,a.useState)("");(0,i.useSearchParams)();let N=(0,l.UU)(),k=async s=>{let t=s||e;if(t){f(!0),j("");try{let{data:e,error:s}=await N.from("orders").select(`
          *,
          pickup_address:addresses!pickup_address_id(address_line_1, city, state),
          delivery_address:addresses!delivery_address_id(address_line_1, city, state),
          delivery_partner:delivery_partners(
            user:users(full_name, phone)
          )
        `).eq("tracking_id",t).single();if(s||!e){j("Order not found. Please check your tracking ID."),h(null),y([]);return}h(e);let{data:r}=await N.from("tracking_events").select("*").eq("order_id",e.id).order("timestamp",{ascending:!1});y(r||[])}catch(e){j("Failed to fetch tracking information")}finally{f(!1)}}},_=e=>{switch(e){case"pending":return(0,r.jsx)(d.A,{className:"h-6 w-6"});case"assigned":return(0,r.jsx)(c.A,{className:"h-6 w-6"});case"picked_up":default:return(0,r.jsx)(o.A,{className:"h-6 w-6"});case"in_transit":return(0,r.jsx)(u.A,{className:"h-6 w-6"});case"delivered":return(0,r.jsx)(p.A,{className:"h-6 w-6"})}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 max-w-4xl",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Track Your Package"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),k()},className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"Enter your tracking ID (e.g., HLD123ABC456)",className:"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)(x.A,{className:"h-5 w-5 text-gray-400 absolute left-4 top-4"})]}),(0,r.jsx)("button",{type:"submit",disabled:v||!e,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Tracking...":"Track"})]}),b&&(0,r.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:b})]}),t&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Order #",t.tracking_id]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Placed on ",(0,n.Yq)(t.created_at)]})]}),(0,r.jsx)("div",{className:`px-4 py-2 rounded-full text-sm font-semibold ${(0,n.qY)(t.status)}`,children:(0,n.Iw)(t.status)})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Pickup Address"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:[t.pickup_address.address_line_1,(0,r.jsx)("br",{}),t.pickup_address.city,", ",t.pickup_address.state]})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"bg-green-100 p-2 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Delivery Address"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:[t.delivery_address.address_line_1,(0,r.jsx)("br",{}),t.delivery_address.city,", ",t.delivery_address.state]})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"bg-purple-100 p-2 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Package Details"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:t.package_description}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm font-semibold",children:["₹",t.total_amount]})]})]})]}),t.estimated_delivery_time&&(0,r.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("span",{className:"font-semibold text-blue-900",children:["Estimated Delivery: ",(0,n.Yq)(t.estimated_delivery_time)]})]})}),t.delivery_partner&&(0,r.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:"Delivery Partner: "}),(0,r.jsx)("span",{className:"text-gray-700",children:t.delivery_partner.user.full_name}),(0,r.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",t.delivery_partner.user.phone,")"]})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Tracking Timeline"}),(0,r.jsx)("div",{className:"space-y-6",children:g.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:`p-2 rounded-full ${0===s?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-600"}`,children:_(e.event_type)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:e.description}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,n.Yq)(e.timestamp)})]}),e.location&&(0,r.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["\uD83D\uDCCD ",e.location]})]})]},e.id))}),0===g.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No tracking events yet"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mt-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Need Help?"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Contact Support"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"If you have any questions about your delivery, our support team is here to help."}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsx)("p",{children:"\uD83D\uDCDE Phone: +91 1234567890"}),(0,r.jsx)("p",{children:"\uD83D\uDCE7 Email: <EMAIL>"}),(0,r.jsx)("p",{children:"\uD83D\uDCAC Live Chat: Available 24/7"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Delivery Status Guide"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-yellow-400 rounded-full"}),(0,r.jsx)("span",{children:"Pending: Order received, waiting for partner assignment"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"}),(0,r.jsx)("span",{children:"Assigned: Delivery partner assigned"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-400 rounded-full"}),(0,r.jsx)("span",{children:"Picked Up: Package collected from pickup location"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-orange-400 rounded-full"}),(0,r.jsx)("span",{children:"In Transit: Package is on the way to destination"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full"}),(0,r.jsx)("span",{children:"Delivered: Package successfully delivered"})]})]})]})]})]})]})})}},8399:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>p,tree:()=>c});var r=t(5239),a=t(8088),i=t(8170),l=t.n(i),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["track",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6809)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\track\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\track\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/track/page",pathname:"/track",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,699,658,530],()=>t(8399));module.exports=r})();