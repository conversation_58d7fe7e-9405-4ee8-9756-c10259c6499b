(()=>{var e={};e.id=774,e.ids=[774],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2442:(e,r,s)=>{Promise.resolve().then(s.bind(s,8134))},2453:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\apd deliary\\\\hyperlocal-delivery\\\\app\\\\book\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\book\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3907:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var t=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let o={children:["",{children:["book",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2453)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\book\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\book\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/book/page",pathname:"/book",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4075:e=>{"use strict";e.exports=require("zlib")},4290:(e,r,s)=>{Promise.resolve().then(s.bind(s,2453))},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6241:(e,r,s)=>{"use strict";function t(){let e=Date.now().toString(36).toUpperCase(),r=Math.random().toString(36).substring(2,6).toUpperCase();return`HLD${e}${r}`}function a(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function i(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function n(e,r,s,t){let a=Math.PI/180*(s-e),i=Math.PI/180*(t-r),n=Math.sin(a/2)*Math.sin(a/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*s)*Math.sin(i/2)*Math.sin(i/2);return 2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n))*6371}function l(e){let r=new Date;return r.setMinutes(r.getMinutes()+(30+5*e)),r}function d(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function o(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}s.d(r,{Iw:()=>o,Q8:()=>l,Yq:()=>i,gd:()=>n,qX:()=>t,qY:()=>d,vv:()=>a})},7910:e=>{"use strict";e.exports=require("stream")},7992:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8134:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(687),a=s(3210),i=s(6189),n=s(6396),l=s(6241),d=s(7992),o=s(6699),c=s(8730);let u=(0,s(2688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);function p(){let[e,r]=(0,a.useState)(null),[s,p]=(0,a.useState)([]),[m,x]=(0,a.useState)({pickupAddressId:"",deliveryAddressId:"",packageDescription:"",packageWeight:"",packageDimensions:"",deliveryInstructions:"",urgency:"standard"}),[g,h]=(0,a.useState)(0),[y,b]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),[k,j]=(0,a.useState)(""),w=(0,i.useRouter)(),N=(0,n.UU)(),_=e=>{x({...m,[e.target.name]:e.target.value})},D=async r=>{r.preventDefault(),v(!0),j("");try{let r=(0,l.qX)(),{data:s,error:t}=await N.from("orders").insert({customer_id:e.id,tracking_id:r,pickup_address_id:m.pickupAddressId,delivery_address_id:m.deliveryAddressId,package_description:m.packageDescription,package_weight:m.packageWeight?parseFloat(m.packageWeight):null,package_dimensions:m.packageDimensions||null,delivery_instructions:m.deliveryInstructions||null,estimated_delivery_time:y?.toISOString(),total_amount:g,status:"pending"}).select().single();if(t)return void j(t.message);await N.from("tracking_events").insert({order_id:s.id,event_type:"order_placed",description:"Order placed successfully",timestamp:new Date().toISOString()}),w.push(`/track?id=${r}`)}catch(e){j("Failed to create order. Please try again.")}finally{v(!1)}};return e?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"container mx-auto px-4 max-w-4xl",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Book a Delivery"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Fill in the details below to book your delivery"})]}),(0,t.jsxs)("form",{onSubmit:D,className:"space-y-8",children:[k&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:k}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),"Pickup Address"]}),(0,t.jsxs)("select",{name:"pickupAddressId",value:m.pickupAddressId,onChange:_,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select pickup address"}),s.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.label," - ",e.address_line_1,", ",e.city]},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),"Delivery Address"]}),(0,t.jsxs)("select",{name:"deliveryAddressId",value:m.deliveryAddressId,onChange:_,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select delivery address"}),s.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.label," - ",e.address_line_1,", ",e.city]},e.id))]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Package Details"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Package Description *"}),(0,t.jsx)("textarea",{name:"packageDescription",value:m.packageDescription,onChange:_,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe what you're sending..."})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Weight (kg)"}),(0,t.jsx)("input",{type:"number",name:"packageWeight",value:m.packageWeight,onChange:_,step:"0.1",min:"0",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 2.5"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Dimensions (L x W x H cm)"}),(0,t.jsx)("input",{type:"text",name:"packageDimensions",value:m.packageDimensions,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 30 x 20 x 10"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delivery Instructions"}),(0,t.jsx)("textarea",{name:"deliveryInstructions",value:m.deliveryInstructions,onChange:_,rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Any special instructions for delivery..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(c.A,{className:"inline h-4 w-4 mr-1"}),"Delivery Urgency"]}),(0,t.jsxs)("select",{name:"urgency",value:m.urgency,onChange:_,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"standard",children:"Standard (Normal delivery)"}),(0,t.jsx)("option",{value:"express",children:"Express (+50% cost, faster delivery)"})]})]})]}),g>0&&(0,t.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(u,{className:"h-5 w-5 mr-2"}),"Cost Summary"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Estimated Cost:"}),(0,t.jsxs)("span",{className:"font-semibold",children:["₹",g]})]}),y&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Estimated Delivery:"}),(0,t.jsx)("span",{className:"font-semibold",children:y.toLocaleString("en-IN",{hour:"2-digit",minute:"2-digit",day:"numeric",month:"short"})})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:f||!m.pickupAddressId||!m.deliveryAddressId,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Booking...":`Book Delivery - ₹${g}`})})]})]})})}):(0,t.jsx)("div",{children:"Loading..."})}},8730:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,699,658,530],()=>s(3907));module.exports=t})();