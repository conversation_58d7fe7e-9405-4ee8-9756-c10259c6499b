import { createBrowserClient } from '@supabase/ssr'
import { createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export function createClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

export async function createServerComponentClient() {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
    },
  })
}

export async function createServerActionClient() {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabaseAnon<PERSON>ey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
      set(name: string, value: string, options: any) {
        cookieStore.set({ name, value, ...options })
      },
      remove(name: string, options: any) {
        cookieStore.set({ name, value: '', ...options })
      },
    },
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          role: 'customer' | 'partner' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          role?: 'customer' | 'partner' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          role?: 'customer' | 'partner' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
      addresses: {
        Row: {
          id: string
          user_id: string
          label: string
          address_line_1: string
          address_line_2: string | null
          city: string
          state: string
          postal_code: string
          latitude: number | null
          longitude: number | null
          is_default: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          label: string
          address_line_1: string
          address_line_2?: string | null
          city: string
          state: string
          postal_code: string
          latitude?: number | null
          longitude?: number | null
          is_default?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          label?: string
          address_line_1?: string
          address_line_2?: string | null
          city?: string
          state?: string
          postal_code?: string
          latitude?: number | null
          longitude?: number | null
          is_default?: boolean
          created_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          customer_id: string
          partner_id: string | null
          tracking_id: string
          pickup_address_id: string
          delivery_address_id: string
          package_description: string
          package_weight: number | null
          package_dimensions: string | null
          delivery_instructions: string | null
          estimated_delivery_time: string | null
          actual_delivery_time: string | null
          status: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'
          total_amount: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          partner_id?: string | null
          tracking_id: string
          pickup_address_id: string
          delivery_address_id: string
          package_description: string
          package_weight?: number | null
          package_dimensions?: string | null
          delivery_instructions?: string | null
          estimated_delivery_time?: string | null
          actual_delivery_time?: string | null
          status?: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'
          total_amount: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          partner_id?: string | null
          tracking_id?: string
          pickup_address_id?: string
          delivery_address_id?: string
          package_description?: string
          package_weight?: number | null
          package_dimensions?: string | null
          delivery_instructions?: string | null
          estimated_delivery_time?: string | null
          actual_delivery_time?: string | null
          status?: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'
          total_amount?: number
          created_at?: string
          updated_at?: string
        }
      }
      delivery_partners: {
        Row: {
          id: string
          user_id: string
          vehicle_type: string
          license_number: string
          document_urls: string[]
          application_status: 'pending' | 'approved' | 'rejected'
          is_active: boolean
          current_latitude: number | null
          current_longitude: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          vehicle_type: string
          license_number: string
          document_urls: string[]
          application_status?: 'pending' | 'approved' | 'rejected'
          is_active?: boolean
          current_latitude?: number | null
          current_longitude?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          vehicle_type?: string
          license_number?: string
          document_urls?: string[]
          application_status?: 'pending' | 'approved' | 'rejected'
          is_active?: boolean
          current_latitude?: number | null
          current_longitude?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      tracking_events: {
        Row: {
          id: string
          order_id: string
          event_type: string
          description: string
          location: string | null
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          order_id: string
          event_type: string
          description: string
          location?: string | null
          timestamp: string
          created_at?: string
        }
        Update: {
          id?: string
          order_id?: string
          event_type?: string
          description?: string
          location?: string | null
          timestamp?: string
          created_at?: string
        }
      }
    }
  }
}
