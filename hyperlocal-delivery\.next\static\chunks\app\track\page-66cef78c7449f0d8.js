(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[970],{646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2672:(e,s,a)=>{Promise.resolve().then(a.bind(a,5385))},3168:(e,s,a)=>{"use strict";a.d(s,{UU:()=>i});var t=a(67),r=a(9509);let l=r.env.NEXT_PUBLIC_SUPABASE_URL,c=r.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function i(){return(0,t.createBrowserClient)(l,c)}},3999:(e,s,a)=>{"use strict";function t(){let e=Date.now().toString(36).toUpperCase(),s=Math.random().toString(36).substring(2,6).toUpperCase();return"".concat("HLD").concat(e).concat(s)}function r(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function l(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function c(e,s,a,t){let r=Math.PI/180*(a-e),l=Math.PI/180*(t-s),c=Math.sin(r/2)*Math.sin(r/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*a)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c))*6371}function i(e){let s=new Date;return s.setMinutes(s.getMinutes()+(30+5*e)),s}function d(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function n(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}a.d(s,{Iw:()=>n,Q8:()=>i,Yq:()=>l,gd:()=>c,qX:()=>t,qY:()=>d,vv:()=>r})},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5385:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(5155),r=a(2115),l=a(5695),c=a(3168),i=a(3999),d=a(4186),n=a(1007),x=a(7108),u=a(9799),m=a(646),o=a(7924),h=a(4516);function p(){let[e,s]=(0,r.useState)(""),[a,p]=(0,r.useState)(null),[g,y]=(0,r.useState)([]),[j,v]=(0,r.useState)(!1),[N,b]=(0,r.useState)(""),f=(0,l.useSearchParams)(),k=(0,c.UU)();(0,r.useEffect)(()=>{let e=f.get("id");e&&(s(e),_(e))},[f]);let _=async s=>{let a=s||e;if(a){v(!0),b("");try{let{data:e,error:s}=await k.from("orders").select("\n          *,\n          pickup_address:addresses!pickup_address_id(address_line_1, city, state),\n          delivery_address:addresses!delivery_address_id(address_line_1, city, state),\n          delivery_partner:delivery_partners(\n            user:users(full_name, phone)\n          )\n        ").eq("tracking_id",a).single();if(s||!e){b("Order not found. Please check your tracking ID."),p(null),y([]);return}p(e);let{data:t}=await k.from("tracking_events").select("*").eq("order_id",e.id).order("timestamp",{ascending:!1});y(t||[])}catch(e){b("Failed to fetch tracking information")}finally{v(!1)}}},w=e=>{switch(e){case"pending":return(0,t.jsx)(d.A,{className:"h-6 w-6"});case"assigned":return(0,t.jsx)(n.A,{className:"h-6 w-6"});case"picked_up":default:return(0,t.jsx)(x.A,{className:"h-6 w-6"});case"in_transit":return(0,t.jsx)(u.A,{className:"h-6 w-6"});case"delivered":return(0,t.jsx)(m.A,{className:"h-6 w-6"})}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 max-w-4xl",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Track Your Package"}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),_()},className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)("input",{type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"Enter your tracking ID (e.g., HLD123ABC456)",className:"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsx)(o.A,{className:"h-5 w-5 text-gray-400 absolute left-4 top-4"})]}),(0,t.jsx)("button",{type:"submit",disabled:j||!e,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:j?"Tracking...":"Track"})]}),N&&(0,t.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:N})]}),a&&(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Order #",a.tracking_id]}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Placed on ",(0,i.Yq)(a.created_at)]})]}),(0,t.jsx)("div",{className:"px-4 py-2 rounded-full text-sm font-semibold ".concat((0,i.qY)(a.status)),children:(0,i.Iw)(a.status)})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Pickup Address"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm",children:[a.pickup_address.address_line_1,(0,t.jsx)("br",{}),a.pickup_address.city,", ",a.pickup_address.state]})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"bg-green-100 p-2 rounded-lg",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Delivery Address"}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm",children:[a.delivery_address.address_line_1,(0,t.jsx)("br",{}),a.delivery_address.city,", ",a.delivery_address.state]})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-2 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Package Details"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:a.package_description}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm font-semibold",children:["₹",a.total_amount]})]})]})]}),a.estimated_delivery_time&&(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("span",{className:"font-semibold text-blue-900",children:["Estimated Delivery: ",(0,i.Yq)(a.estimated_delivery_time)]})]})}),a.delivery_partner&&(0,t.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-semibold text-gray-900",children:"Delivery Partner: "}),(0,t.jsx)("span",{className:"text-gray-700",children:a.delivery_partner.user.full_name}),(0,t.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",a.delivery_partner.user.phone,")"]})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Tracking Timeline"}),(0,t.jsx)("div",{className:"space-y-6",children:g.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"p-2 rounded-full ".concat(0===s?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-600"),children:w(e.event_type)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:e.description}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:(0,i.Yq)(e.timestamp)})]}),e.location&&(0,t.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["\uD83D\uDCCD ",e.location]})]})]},e.id))}),0===g.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No tracking events yet"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mt-8",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Need Help?"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Contact Support"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:"If you have any questions about your delivery, our support team is here to help."}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsx)("p",{children:"\uD83D\uDCDE Phone: +91 1234567890"}),(0,t.jsx)("p",{children:"\uD83D\uDCE7 Email: <EMAIL>"}),(0,t.jsx)("p",{children:"\uD83D\uDCAC Live Chat: Available 24/7"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Delivery Status Guide"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-400 rounded-full"}),(0,t.jsx)("span",{children:"Pending: Order received, waiting for partner assignment"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"}),(0,t.jsx)("span",{children:"Assigned: Delivery partner assigned"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-purple-400 rounded-full"}),(0,t.jsx)("span",{children:"Picked Up: Package collected from pickup location"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-orange-400 rounded-full"}),(0,t.jsx)("span",{children:"In Transit: Package is on the way to destination"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full"}),(0,t.jsx)("span",{children:"Delivered: Package successfully delivered"})]})]})]})]})]})]})})}},7108:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7924:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9799:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[214,441,684,358],()=>s(2672)),_N_E=e.O()}]);