(()=>{var e={};e.id=859,e.ids=[859],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1534:(e,t,r)=>{Promise.resolve().then(r.bind(r,9423))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(687),a=r(3210),i=r(6189),l=r(5814),o=r.n(l),n=r(6396),d=r(3931),c=r(4021),u=r(2597),p=r(3861);function m(){let[e,t]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[m,x]=(0,a.useState)(!1),[h,y]=(0,a.useState)(!1),[f,g]=(0,a.useState)(""),b=(0,i.useRouter)(),v=(0,n.UU)(),w=async t=>{t.preventDefault(),y(!0),g("");try{let{data:t,error:s}=await v.auth.signInWithPassword({email:e,password:r});if(s)return void g(s.message);if(t.user){let{data:e}=await v.from("users").select("role").eq("id",t.user.id).single();e?.role==="admin"?b.push("/admin"):e?.role==="partner"?b.push("/partner/dashboard"):b.push("/dashboard")}}catch(e){g("An unexpected error occurred")}finally{y(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(o(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:w,children:[f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:f}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"}),(0,s.jsx)(d.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>l(e.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"}),(0,s.jsx)(c.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!m),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:m?(0,s.jsx)(u.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(o(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Signing in...":"Sign in"})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",onClick:async()=>{await v.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}})},children:[(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,s.jsx)("span",{className:"ml-2",children:"Sign in with Google"})]})})]})]})})]})}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9141:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),l=r.n(i),o=r(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9423)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9423:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\apd deliary\\\\hyperlocal-delivery\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\login\\page.tsx","default")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9990:(e,t,r)=>{Promise.resolve().then(r.bind(r,5038))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,699,658,530],()=>r(9141));module.exports=s})();