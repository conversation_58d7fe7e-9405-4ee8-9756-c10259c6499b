(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{1007:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2657:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3168:(e,r,a)=>{"use strict";a.d(r,{UU:()=>n});var s=a(67),l=a(9509);let t=l.env.NEXT_PUBLIC_SUPABASE_URL,o=l.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function n(){return(0,s.createBrowserClient)(t,o)}},3726:(e,r,a)=>{Promise.resolve().then(a.bind(a,4388))},4388:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>x});var s=a(5155),l=a(2115),t=a(5695),o=a(6874),n=a.n(o),d=a(3168),c=a(1007),i=a(8883);let u=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var m=a(2919),p=a(8749),h=a(2657);function x(){let[e,r]=(0,l.useState)({fullName:"",email:"",phone:"",password:"",confirmPassword:"",role:"customer"}),[a,o]=(0,l.useState)(!1),[x,f]=(0,l.useState)(!1),[b,y]=(0,l.useState)(!1),[g,w]=(0,l.useState)(""),N=(0,t.useRouter)(),v=(0,d.UU)(),j=a=>{r({...e,[a.target.name]:a.target.value})},k=async r=>{if(r.preventDefault(),y(!0),w(""),e.password!==e.confirmPassword){w("Passwords do not match"),y(!1);return}if(e.password.length<6){w("Password must be at least 6 characters long"),y(!1);return}try{let{data:r,error:a}=await v.auth.signUp({email:e.email,password:e.password,options:{data:{full_name:e.fullName,phone:e.phone,role:e.role}}});if(a)return void w(a.message);if(r.user){let{error:a}=await v.from("users").insert({id:r.user.id,email:e.email,full_name:e.fullName,phone:e.phone,role:e.role});a&&console.error("Profile creation error:",a),"partner"===e.role?N.push("/partner/register"):N.push("/dashboard")}}catch(e){w("An unexpected error occurred")}finally{y(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(n(),{href:"/auth/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:k,children:[g&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:g}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"fullName",name:"fullName",type:"text",required:!0,value:e.fullName,onChange:j,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your full name"}),(0,s.jsx)(c.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:j,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"}),(0,s.jsx)(i.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,value:e.phone,onChange:j,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your phone number"}),(0,s.jsx)(u,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,s.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:j,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"customer",children:"Customer"}),(0,s.jsx)("option",{value:"partner",children:"Delivery Partner"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:a?"text":"password",required:!0,value:e.password,onChange:j,className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"}),(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,s.jsx)("button",{type:"button",onClick:()=>o(!a),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:a?(0,s.jsx)(p.A,{className:"h-5 w-5"}):(0,s.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:x?"text":"password",required:!0,value:e.confirmPassword,onChange:j,className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm your password"}),(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,s.jsx)("button",{type:"button",onClick:()=>f(!x),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:x?(0,s.jsx)(p.A,{className:"h-5 w-5"}):(0,s.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:b,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"Creating account...":"Create account"})})]})})})]})}},8749:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[214,874,441,684,358],()=>r(3726)),_N_E=e.O()}]);