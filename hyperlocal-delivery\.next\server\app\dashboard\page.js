(()=>{var e={};e.id=105,e.ids=[105],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4118:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\apd deliary\\\\hyperlocal-delivery\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\dashboard\\page.tsx","default")},4325:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4118)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6241:(e,s,r)=>{"use strict";function t(){let e=Date.now().toString(36).toUpperCase(),s=Math.random().toString(36).substring(2,6).toUpperCase();return`HLD${e}${s}`}function a(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function i(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function l(e,s,r,t){let a=Math.PI/180*(r-e),i=Math.PI/180*(t-s),l=Math.sin(a/2)*Math.sin(a/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*r)*Math.sin(i/2)*Math.sin(i/2);return 2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l))*6371}function n(e){let s=new Date;return s.setMinutes(s.getMinutes()+(30+5*e)),s}function d(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function c(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}r.d(s,{Iw:()=>c,Q8:()=>n,Yq:()=>i,gd:()=>l,qX:()=>t,qY:()=>d,vv:()=>a})},6569:(e,s,r)=>{Promise.resolve().then(r.bind(r,4118))},7193:(e,s,r)=>{Promise.resolve().then(r.bind(r,9680))},7910:e=>{"use strict";e.exports=require("stream")},7992:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8730:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9270:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9680:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(687),a=r(3210),i=r(6189),l=r(5814),n=r.n(l),d=r(6396),c=r(6241),o=r(6699),u=r(8730);let x=(0,r(2688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var p=r(9270),h=r(7992),m=r(3861);function g(){let[e,s]=(0,a.useState)(null),[r,l]=(0,a.useState)([]),[g,b]=(0,a.useState)(!0),[y,f]=(0,a.useState)(""),[v,j]=(0,a.useState)("all");(0,i.useRouter)(),(0,d.UU)();let N=r.filter(e=>{let s=e.tracking_id.toLowerCase().includes(y.toLowerCase())||e.package_description.toLowerCase().includes(y.toLowerCase()),r="all"===v||e.status===v;return s&&r}),w=(()=>{let e=r.length,s=r.filter(e=>"pending"===e.status).length;return{total:e,pending:s,inTransit:r.filter(e=>["assigned","picked_up","in_transit"].includes(e.status)).length,delivered:r.filter(e=>"delivered"===e.status).length}})();return g?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 max-w-6xl",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",e?.user_metadata?.full_name||e?.email?.split("@")[0],"!"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your deliveries and track your packages"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.total})]}),(0,t.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:w.pending})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Transit"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:w.inTransit})]}),(0,t.jsx)(o.A,{className:"h-8 w-8 text-orange-600"})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Delivered"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:w.delivered})]}),(0,t.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(n(),{href:"/book",className:"flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors",children:[(0,t.jsx)(x,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold text-blue-600",children:"Book New Delivery"})]}),(0,t.jsxs)(n(),{href:"/track",className:"flex items-center justify-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-green-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold text-green-600",children:"Track Package"})]}),(0,t.jsxs)(n(),{href:"/addresses",className:"flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors",children:[(0,t.jsx)(h.A,{className:"h-6 w-6 text-purple-600 mr-2"}),(0,t.jsx)("span",{className:"font-semibold text-purple-600",children:"Manage Addresses"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Your Orders"}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search orders...",value:y,onChange:e=>f(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]}),(0,t.jsxs)("select",{value:v,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"assigned",children:"Assigned"}),(0,t.jsx)("option",{value:"picked_up",children:"Picked Up"}),(0,t.jsx)("option",{value:"in_transit",children:"In Transit"}),(0,t.jsx)("option",{value:"delivered",children:"Delivered"}),(0,t.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]})]})}),(0,t.jsx)("div",{className:"p-6",children:0===N.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:0===r.length?"No orders yet":"No orders match your search"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:0===r.length?"Start by booking your first delivery":"Try adjusting your search or filter criteria"}),0===r.length&&(0,t.jsxs)(n(),{href:"/book",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,t.jsx)(x,{className:"h-5 w-5 mr-2"}),"Book Your First Delivery"]})]}):(0,t.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-900",children:["#",e.tracking_id]}),(0,t.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-semibold ${(0,c.qY)(e.status)}`,children:(0,c.Iw)(e.status)})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-2",children:e.package_description}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1 text-blue-600"}),(0,t.jsxs)("span",{children:["From: ",e.pickup_address.address_line_1,", ",e.pickup_address.city]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1 text-green-600"}),(0,t.jsxs)("span",{children:["To: ",e.delivery_address.address_line_1,", ",e.delivery_address.city]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["Ordered: ",(0,c.Yq)(e.created_at)]}),e.estimated_delivery_time&&(0,t.jsxs)("span",{children:["ETA: ",(0,c.Yq)(e.estimated_delivery_time)]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("p",{className:"font-semibold text-gray-900",children:(0,c.vv)(e.total_amount)})}),(0,t.jsxs)(n(),{href:`/track?id=${e.tracking_id}`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Track"]})]})]})},e.id))})})]})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,699,658,530],()=>r(4325));module.exports=t})();