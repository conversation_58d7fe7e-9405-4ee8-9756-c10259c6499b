'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase'
import { generateTrackingId, estimateDeliveryTime, calculateDistance } from '@/lib/utils'
import { MapPin, Package, Clock, CreditCard } from 'lucide-react'

interface Address {
  id: string
  label: string
  address_line_1: string
  address_line_2?: string
  city: string
  state: string
  postal_code: string
  latitude?: number
  longitude?: number
}

export default function BookDeliveryPage() {
  const [user, setUser] = useState<any>(null)
  const [addresses, setAddresses] = useState<Address[]>([])
  const [formData, setFormData] = useState({
    pickupAddressId: '',
    deliveryAddressId: '',
    packageDescription: '',
    packageWeight: '',
    packageDimensions: '',
    deliveryInstructions: '',
    urgency: 'standard'
  })
  const [estimatedCost, setEstimatedCost] = useState(0)
  const [estimatedTime, setEstimatedTime] = useState<Date | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }
      setUser(user)

      // Fetch user addresses
      const { data: addressData } = await supabase
        .from('addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })

      setAddresses(addressData || [])
    }

    getUser()
  }, [supabase, router])

  useEffect(() => {
    // Calculate estimated cost and time when addresses change
    if (formData.pickupAddressId && formData.deliveryAddressId) {
      const pickupAddress = addresses.find(a => a.id === formData.pickupAddressId)
      const deliveryAddress = addresses.find(a => a.id === formData.deliveryAddressId)

      if (pickupAddress?.latitude && pickupAddress?.longitude && 
          deliveryAddress?.latitude && deliveryAddress?.longitude) {
        const distance = calculateDistance(
          pickupAddress.latitude,
          pickupAddress.longitude,
          deliveryAddress.latitude,
          deliveryAddress.longitude
        )

        // Base cost calculation
        const baseCost = 50 // Base cost in INR
        const costPerKm = 10
        const urgencyMultiplier = formData.urgency === 'express' ? 1.5 : 1
        const weightMultiplier = formData.packageWeight ? Math.max(1, parseFloat(formData.packageWeight) / 5) : 1

        const totalCost = (baseCost + (distance * costPerKm)) * urgencyMultiplier * weightMultiplier
        setEstimatedCost(Math.round(totalCost))

        // Estimate delivery time
        const estimatedDeliveryTime = estimateDeliveryTime(distance)
        if (formData.urgency === 'express') {
          estimatedDeliveryTime.setMinutes(estimatedDeliveryTime.getMinutes() - 15)
        }
        setEstimatedTime(estimatedDeliveryTime)
      }
    }
  }, [formData, addresses])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const trackingId = generateTrackingId()

      const { data, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_id: user.id,
          tracking_id: trackingId,
          pickup_address_id: formData.pickupAddressId,
          delivery_address_id: formData.deliveryAddressId,
          package_description: formData.packageDescription,
          package_weight: formData.packageWeight ? parseFloat(formData.packageWeight) : null,
          package_dimensions: formData.packageDimensions || null,
          delivery_instructions: formData.deliveryInstructions || null,
          estimated_delivery_time: estimatedTime?.toISOString(),
          total_amount: estimatedCost,
          status: 'pending'
        })
        .select()
        .single()

      if (orderError) {
        setError(orderError.message)
        return
      }

      // Create initial tracking event
      await supabase
        .from('tracking_events')
        .insert({
          order_id: data.id,
          event_type: 'order_placed',
          description: 'Order placed successfully',
          timestamp: new Date().toISOString()
        })

      // Redirect to tracking page
      router.push(`/track?id=${trackingId}`)
    } catch {
      setError('Failed to create order. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Book a Delivery</h1>
            <p className="text-gray-600">Fill in the details below to book your delivery</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                {error}
              </div>
            )}

            {/* Addresses Section */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Pickup Address
                </label>
                <select
                  name="pickupAddressId"
                  value={formData.pickupAddressId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select pickup address</option>
                  {addresses.map((address) => (
                    <option key={address.id} value={address.id}>
                      {address.label} - {address.address_line_1}, {address.city}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Delivery Address
                </label>
                <select
                  name="deliveryAddressId"
                  value={formData.deliveryAddressId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select delivery address</option>
                  {addresses.map((address) => (
                    <option key={address.id} value={address.id}>
                      {address.label} - {address.address_line_1}, {address.city}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Package Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Package Details
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Package Description *
                </label>
                <textarea
                  name="packageDescription"
                  value={formData.packageDescription}
                  onChange={handleInputChange}
                  required
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe what you're sending..."
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Weight (kg)
                  </label>
                  <input
                    type="number"
                    name="packageWeight"
                    value={formData.packageWeight}
                    onChange={handleInputChange}
                    step="0.1"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 2.5"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dimensions (L x W x H cm)
                  </label>
                  <input
                    type="text"
                    name="packageDimensions"
                    value={formData.packageDimensions}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 30 x 20 x 10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delivery Instructions
                </label>
                <textarea
                  name="deliveryInstructions"
                  value={formData.deliveryInstructions}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Any special instructions for delivery..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock className="inline h-4 w-4 mr-1" />
                  Delivery Urgency
                </label>
                <select
                  name="urgency"
                  value={formData.urgency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="standard">Standard (Normal delivery)</option>
                  <option value="express">Express (+50% cost, faster delivery)</option>
                </select>
              </div>
            </div>

            {/* Cost Summary */}
            {estimatedCost > 0 && (
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Cost Summary
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Estimated Cost:</span>
                    <span className="font-semibold">₹{estimatedCost}</span>
                  </div>
                  {estimatedTime && (
                    <div className="flex justify-between">
                      <span>Estimated Delivery:</span>
                      <span className="font-semibold">
                        {estimatedTime.toLocaleString('en-IN', {
                          hour: '2-digit',
                          minute: '2-digit',
                          day: 'numeric',
                          month: 'short'
                        })}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading || !formData.pickupAddressId || !formData.deliveryAddressId}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Booking...' : `Book Delivery - ₹${estimatedCost}`}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
