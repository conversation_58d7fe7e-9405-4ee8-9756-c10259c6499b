(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3168:(e,s,t)=>{"use strict";t.d(s,{UU:()=>d});var r=t(67),a=t(9509);let l=a.env.NEXT_PUBLIC_SUPABASE_URL,i=a.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function d(){return(0,r.createBrowserClient)(l,i)}},4162:(e,s,t)=>{Promise.resolve().then(t.bind(t,9598))},8749:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9598:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(5155),a=t(2115),l=t(5695),i=t(6874),d=t.n(i),o=t(3168),n=t(8883),c=t(2919),u=t(8749),m=t(2657);function h(){let[e,s]=(0,a.useState)(""),[t,i]=(0,a.useState)(""),[h,x]=(0,a.useState)(!1),[p,b]=(0,a.useState)(!1),[f,y]=(0,a.useState)(""),g=(0,l.useRouter)(),w=(0,o.UU)(),v=async s=>{s.preventDefault(),b(!0),y("");try{let{data:s,error:r}=await w.auth.signInWithPassword({email:e,password:t});if(r)return void y(r.message);if(s.user){let{data:e}=await w.from("users").select("role").eq("id",s.user.id).single();(null==e?void 0:e.role)==="admin"?g.push("/admin"):(null==e?void 0:e.role)==="partner"?g.push("/partner/dashboard"):g.push("/dashboard")}}catch(e){y("An unexpected error occurred")}finally{b(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Sign in to your account"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,r.jsx)(d(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,r.jsxs)("form",{className:"space-y-6",onSubmit:v,children:[f&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:f}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,r.jsxs)("div",{className:"mt-1 relative",children:[(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"}),(0,r.jsx)(n.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"mt-1 relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>i(e.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"}),(0,r.jsx)(c.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,r.jsx)("button",{type:"button",onClick:()=>x(!h),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:h?(0,r.jsx)(u.A,{className:"h-5 w-5"}):(0,r.jsx)(m.A,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)(d(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:p,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Signing in...":"Sign in"})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{type:"button",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",onClick:async()=>{await w.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}})},children:[(0,r.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,r.jsx)("span",{className:"ml-2",children:"Sign in with Google"})]})})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[214,874,441,684,358],()=>s(4162)),_N_E=e.O()}]);