# HyperLocal Delivery Service

A modern, user-friendly website for a hyperlocal delivery service built with Next.js, TypeScript, Tailwind CSS, and Supabase.

## Features

### Customer Features
- **User Registration & Login**: Secure user accounts with role-based access
- **Parcel Booking**: Intuitive booking form with pickup and delivery details
- **Real-time Tracking**: Track packages with live updates and estimated delivery times
- **Order History**: View past orders and manage delivery preferences
- **Address Management**: Save multiple addresses for quick booking
- **Mobile-Responsive Design**: Optimized for mobile, tablet, and desktop

### Delivery Partner Features
- **Partner Registration**: Dedicated signup with document upload
- **Application Tracking**: Monitor application status and approval process
- **Partner Dashboard**: Manage active deliveries and earnings
- **Document Management**: Upload and manage required documents

### Admin Features
- **Admin Dashboard**: Comprehensive overview of orders, users, and partners
- **Order Management**: Track and manage all deliveries
- **Partner Approval**: Review and approve delivery partner applications
- **User Management**: Monitor and manage customer accounts
- **Analytics**: View system statistics and performance metrics

## Technology Stack

- **Frontend**: Next.js 14 with React and TypeScript
- **Styling**: Tailwind CSS with custom components
- **Backend**: Next.js API routes
- **Database**: PostgreSQL via Supabase
- **Authentication**: Supabase Auth with Row Level Security
- **Real-time**: Supabase Realtime for live updates
- **File Storage**: Supabase Storage for document uploads
- **Icons**: Lucide React
- **Forms**: React Hook Form with Zod validation

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- A Supabase account and project

### 1. Clone and Install

```bash
git clone <repository-url>
cd hyperlocal-delivery
npm install
```

### 2. Environment Setup

Create a `.env.local` file in the root directory:

```bash
cp .env.local.example .env.local
```

Update the environment variables with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="HyperLocal Delivery"
```

### 3. Database Setup

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database-setup.sql`
4. Run the script to create all necessary tables, indexes, and policies

### 4. Storage Setup

1. In your Supabase dashboard, go to Storage
2. The `partner-documents` bucket should be created automatically by the SQL script
3. If not, create it manually with the name `partner-documents`

### 5. Admin User Setup

1. First, sign up through the application with your admin email
2. Then run this SQL query in Supabase to make yourself an admin:

```sql
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

### 6. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Project Structure

```
hyperlocal-delivery/
├── app/                          # Next.js app directory
│   ├── admin/                    # Admin dashboard pages
│   ├── auth/                     # Authentication pages
│   ├── book/                     # Booking page
│   ├── dashboard/                # Customer dashboard
│   ├── partner/                  # Partner pages
│   ├── track/                    # Tracking page
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Landing page
├── components/                   # Reusable components
│   ├── navigation/               # Navigation components
│   └── ui/                       # UI components
├── lib/                          # Utility libraries
│   ├── supabase.ts              # Supabase client and types
│   └── utils.ts                 # Utility functions
├── public/                       # Static assets
├── database-setup.sql            # Database schema and setup
└── README.md                     # This file
```

## Key Features Implementation

### Authentication & Authorization
- Supabase Auth with email/password and OAuth (Google)
- Role-based access control (customer, partner, admin)
- Row Level Security (RLS) policies for data protection

### Real-time Tracking
- Live order status updates
- Tracking events with timestamps and locations
- Estimated delivery time calculations

### File Upload
- Secure document upload for delivery partners
- Supabase Storage with access policies
- Support for PDF, JPG, PNG formats

### Mobile-First Design
- Responsive design with Tailwind CSS
- Touch-friendly interface
- Progressive Web App ready

## Database Schema

### Core Tables
- `users` - User profiles with roles
- `addresses` - Customer delivery addresses
- `orders` - Delivery orders with status tracking
- `delivery_partners` - Partner profiles and applications
- `tracking_events` - Order tracking history

### Security
- Row Level Security (RLS) enabled on all tables
- Users can only access their own data
- Admins have elevated permissions
- Partners can only see assigned orders

## API Routes

The application uses Next.js API routes for:
- Order management
- Partner application processing
- Admin operations
- File upload handling

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The application can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]
