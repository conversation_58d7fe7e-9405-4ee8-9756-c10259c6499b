(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[865],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3168:(e,s,t)=>{"use strict";t.d(s,{UU:()=>c});var a=t(67),l=t(9509);let r=l.env.NEXT_PUBLIC_SUPABASE_URL,i=l.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function c(){return(0,a.createBrowserClient)(r,i)}},3186:(e,s,t)=>{Promise.resolve().then(t.bind(t,7152))},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7152:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),l=t(2115),r=t(5695),i=t(3168),c=t(646),n=t(9799),d=t(5339),o=t(9946);let m=(0,o.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),u=(0,o.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function x(){let[e,s]=(0,l.useState)(null),[t,o]=(0,l.useState)({vehicleType:"",licenseNumber:"",documents:[]}),[x,h]=(0,l.useState)(!1),[p,b]=(0,l.useState)(""),[y,g]=(0,l.useState)(!1),j=(0,r.useRouter)(),v=(0,i.UU)();(0,l.useEffect)(()=>{(async()=>{let{data:{user:e}}=await v.auth.getUser();if(!e)return j.push("/auth/login");s(e);let{data:t}=await v.from("delivery_partners").select("*").eq("user_id",e.id).single();t&&j.push("/partner/dashboard")})()},[v,j]);let N=e=>{o({...t,[e.target.name]:e.target.value})},f=async s=>Promise.all(s.map(async s=>{let t="".concat(e.id,"/").concat(Date.now(),"_").concat(s.name),{data:a,error:l}=await v.storage.from("partner-documents").upload(t,s);if(l)throw l;return t})),w=async s=>{s.preventDefault(),h(!0),b("");try{let s=await f(t.documents),{error:a}=await v.from("delivery_partners").insert({user_id:e.id,vehicle_type:t.vehicleType,license_number:t.licenseNumber,document_urls:s,application_status:"pending"});if(a)return void b(a.message);g(!0),setTimeout(()=>{j.push("/partner/dashboard")},2e3)}catch(e){b("Failed to submit application. Please try again.")}finally{h(!1)}};return e?y?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-lg text-center max-w-md",children:[(0,a.jsx)(c.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Application Submitted!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Your delivery partner application has been submitted successfully. We'll review it and get back to you within 24-48 hours."}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to dashboard..."})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"container mx-auto px-4 max-w-2xl",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(n.A,{className:"h-16 w-16 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Become a Delivery Partner"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Join our network and start earning with flexible schedules"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg mb-8",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-900 mb-4",children:"Partner Benefits"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{children:"Flexible working hours"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{children:"Competitive earnings"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{children:"Weekly payments"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{children:"Insurance coverage"})]})]})]}),(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[p&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md flex items-center",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-2"}),p]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vehicle Type *"}),(0,a.jsxs)("select",{name:"vehicleType",value:t.vehicleType,onChange:N,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select your vehicle type"}),(0,a.jsx)("option",{value:"bicycle",children:"Bicycle"}),(0,a.jsx)("option",{value:"motorcycle",children:"Motorcycle"}),(0,a.jsx)("option",{value:"scooter",children:"Scooter"}),(0,a.jsx)("option",{value:"car",children:"Car"}),(0,a.jsx)("option",{value:"van",children:"Van"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Driving License Number *"}),(0,a.jsx)("input",{type:"text",name:"licenseNumber",value:t.licenseNumber,onChange:N,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your license number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Required Documents *"}),(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("label",{className:"cursor-pointer",children:[(0,a.jsx)("span",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Choose Files"}),(0,a.jsx)("input",{type:"file",multiple:!0,accept:".pdf,.jpg,.jpeg,.png",onChange:e=>{e.target.files&&o({...t,documents:Array.from(e.target.files)})},className:"hidden",required:!0})]})}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Upload the following documents (PDF, JPG, PNG):"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-500 space-y-1",children:[(0,a.jsx)("li",{children:"• Driving License (both sides)"}),(0,a.jsx)("li",{children:"• Vehicle Registration Certificate"}),(0,a.jsx)("li",{children:"• Insurance Certificate"}),(0,a.jsx)("li",{children:"• Aadhaar Card"}),(0,a.jsx)("li",{children:"• Bank Account Details"})]})]})}),t.documents.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Selected Files:"}),(0,a.jsx)("div",{className:"space-y-2",children:t.documents.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"ml-auto text-gray-400",children:[(e.size/1024/1024).toFixed(2)," MB"]})]},s))})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-yellow-600 mr-2 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-medium text-yellow-800 mb-1",children:"Application Review Process"}),(0,a.jsx)("p",{className:"text-yellow-700",children:"Your application will be reviewed within 24-48 hours. We'll verify your documents and conduct a background check. You'll receive an email notification once approved."})]})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"submit",disabled:x||0===t.documents.length,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Submitting...":"Submit Application"})})]})]})})}):(0,a.jsx)("div",{children:"Loading..."})}},9799:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[214,441,684,358],()=>s(3186)),_N_E=e.O()}]);