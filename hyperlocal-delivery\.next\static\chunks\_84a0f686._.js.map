{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/apd%20deliary/hyperlocal-delivery/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\nimport { createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''\n\nexport function createClient() {\n  if (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables')\n  }\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\nexport async function createServerComponentClient() {\n  if (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables')\n  }\n\n  const { cookies } = await import('next/headers')\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n    },\n  })\n}\n\nexport async function createServerActionClient() {\n  if (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables')\n  }\n\n  const { cookies } = await import('next/headers')\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n      set(name: string, value: string, options: any) {\n        cookieStore.set({ name, value, ...options })\n      },\n      remove(name: string, options: any) {\n        cookieStore.set({ name, value: '', ...options })\n      },\n    },\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          phone: string | null\n          role: 'customer' | 'partner' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          phone?: string | null\n          role?: 'customer' | 'partner' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          phone?: string | null\n          role?: 'customer' | 'partner' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      addresses: {\n        Row: {\n          id: string\n          user_id: string\n          label: string\n          address_line_1: string\n          address_line_2: string | null\n          city: string\n          state: string\n          postal_code: string\n          latitude: number | null\n          longitude: number | null\n          is_default: boolean\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          label: string\n          address_line_1: string\n          address_line_2?: string | null\n          city: string\n          state: string\n          postal_code: string\n          latitude?: number | null\n          longitude?: number | null\n          is_default?: boolean\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          label?: string\n          address_line_1?: string\n          address_line_2?: string | null\n          city?: string\n          state?: string\n          postal_code?: string\n          latitude?: number | null\n          longitude?: number | null\n          is_default?: boolean\n          created_at?: string\n        }\n      }\n      orders: {\n        Row: {\n          id: string\n          customer_id: string\n          partner_id: string | null\n          tracking_id: string\n          pickup_address_id: string\n          delivery_address_id: string\n          package_description: string\n          package_weight: number | null\n          package_dimensions: string | null\n          delivery_instructions: string | null\n          estimated_delivery_time: string | null\n          actual_delivery_time: string | null\n          status: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'\n          total_amount: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          customer_id: string\n          partner_id?: string | null\n          tracking_id: string\n          pickup_address_id: string\n          delivery_address_id: string\n          package_description: string\n          package_weight?: number | null\n          package_dimensions?: string | null\n          delivery_instructions?: string | null\n          estimated_delivery_time?: string | null\n          actual_delivery_time?: string | null\n          status?: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'\n          total_amount: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          customer_id?: string\n          partner_id?: string | null\n          tracking_id?: string\n          pickup_address_id?: string\n          delivery_address_id?: string\n          package_description?: string\n          package_weight?: number | null\n          package_dimensions?: string | null\n          delivery_instructions?: string | null\n          estimated_delivery_time?: string | null\n          actual_delivery_time?: string | null\n          status?: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'\n          total_amount?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      delivery_partners: {\n        Row: {\n          id: string\n          user_id: string\n          vehicle_type: string\n          license_number: string\n          document_urls: string[]\n          application_status: 'pending' | 'approved' | 'rejected'\n          is_active: boolean\n          current_latitude: number | null\n          current_longitude: number | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          vehicle_type: string\n          license_number: string\n          document_urls: string[]\n          application_status?: 'pending' | 'approved' | 'rejected'\n          is_active?: boolean\n          current_latitude?: number | null\n          current_longitude?: number | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          vehicle_type?: string\n          license_number?: string\n          document_urls?: string[]\n          application_status?: 'pending' | 'approved' | 'rejected'\n          is_active?: boolean\n          current_latitude?: number | null\n          current_longitude?: number | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      tracking_events: {\n        Row: {\n          id: string\n          order_id: string\n          event_type: string\n          description: string\n          location: string | null\n          timestamp: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          order_id: string\n          event_type: string\n          description: string\n          location?: string | null\n          timestamp: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          order_id?: string\n          event_type?: string\n          description?: string\n          location?: string | null\n          timestamp?: string\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AAAA;AACA;;;AAEA,MAAM,cAAc,sEAAwC;AAC5D,MAAM,kBAAkB,mEAA6C;AAE9D,SAAS;IACd,uCAAsC;;IAEtC;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAEO,eAAe;IACpB,uCAAsC;;IAEtC;IAEA,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,MAAM,cAAc,MAAM;IAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;QACF;IACF;AACF;AAEO,eAAe;IACpB,uCAAsC;;IAEtC;IAEA,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,MAAM,cAAc,MAAM;IAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/apd%20deliary/hyperlocal-delivery/components/navigation/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase'\nimport { User } from '@supabase/supabase-js'\nimport {\n  Menu,\n  X,\n  Package,\n  User as UserIcon,\n  LogOut,\n  Settings,\n  Truck,\n  BarChart3\n} from 'lucide-react'\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [user, setUser] = useState<User | null>(null)\n  const [userRole, setUserRole] = useState<string | null>(null)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      \n      if (user) {\n        const { data: profile } = await supabase\n          .from('users')\n          .select('role')\n          .eq('id', user.id)\n          .single()\n        \n        setUserRole(profile?.role || null)\n      }\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user || null)\n        if (session?.user) {\n          const { data: profile } = await supabase\n            .from('users')\n            .select('role')\n            .eq('id', session.user.id)\n            .single()\n          \n          setUserRole(profile?.role || null)\n        } else {\n          setUserRole(null)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase])\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n    router.push('/')\n  }\n\n  const navLinks = [\n    { href: '/', label: 'Home' },\n    { href: '/book', label: 'Book Delivery' },\n    { href: '/track', label: 'Track Package' },\n  ]\n\n  const userMenuItems = user ? [\n    { href: '/dashboard', label: 'Dashboard', icon: UserIcon },\n    ...(userRole === 'partner' ? [\n      { href: '/partner/dashboard', label: 'Partner Dashboard', icon: Truck }\n    ] : []),\n    ...(userRole === 'admin' ? [\n      { href: '/admin', label: 'Admin Panel', icon: BarChart3 }\n    ] : []),\n    { href: '/settings', label: 'Settings', icon: Settings },\n  ] : []\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Package className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">\n              HyperLocal Delivery\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.href}\n                href={link.href}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                {link.label}\n              </Link>\n            ))}\n            \n            {!user ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Sign Up\n                </Link>\n              </div>\n            ) : (\n              <div className=\"relative group\">\n                <button className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600\">\n                  <UserIcon className=\"h-5 w-5\" />\n                  <span>{user.email?.split('@')[0]}</span>\n                </button>\n                \n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                  {userMenuItems.map((item) => (\n                    <Link\n                      key={item.href}\n                      href={item.href}\n                      className=\"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 first:rounded-t-lg\"\n                    >\n                      <item.icon className=\"h-4 w-4\" />\n                      <span>{item.label}</span>\n                    </Link>\n                  ))}\n                  <button\n                    onClick={handleSignOut}\n                    className=\"flex items-center space-x-2 w-full px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-b-lg\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span>Sign Out</span>\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2\"\n          >\n            {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.href}\n                href={link.href}\n                className=\"block py-2 text-gray-700 hover:text-blue-600\"\n                onClick={() => setIsOpen(false)}\n              >\n                {link.label}\n              </Link>\n            ))}\n            \n            {!user ? (\n              <div className=\"pt-4 space-y-2\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"block py-2 text-gray-700 hover:text-blue-600\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-center\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  Sign Up\n                </Link>\n              </div>\n            ) : (\n              <div className=\"pt-4 space-y-2\">\n                {userMenuItems.map((item) => (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className=\"flex items-center space-x-2 py-2 text-gray-700 hover:text-blue-600\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <item.icon className=\"h-4 w-4\" />\n                    <span>{item.label}</span>\n                  </Link>\n                ))}\n                <button\n                  onClick={() => {\n                    handleSignOut()\n                    setIsOpen(false)\n                  }}\n                  className=\"flex items-center space-x-2 py-2 text-gray-700 hover:text-blue-600\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Sign Out</span>\n                </button>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAkBO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;gDAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBACtD,QAAQ;oBAER,IAAI,MAAM;wBACR,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,YAAY,SAAS,QAAQ;oBAC/B;gBACF;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;wCAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,IAAI,SAAS,MAAM;wBACjB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;wBAET,YAAY,SAAS,QAAQ;oBAC/B,OAAO;wBACL,YAAY;oBACd;gBACF;;YAGF;wCAAO,IAAM,aAAa,WAAW;;QACvC;+BAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAU,OAAO;QAAgB;KAC1C;IAED,MAAM,gBAAgB,OAAO;QAC3B;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM,qMAAA,CAAA,OAAQ;QAAC;WACrD,aAAa,YAAY;YAC3B;gBAAE,MAAM;gBAAsB,OAAO;gBAAqB,MAAM,uMAAA,CAAA,QAAK;YAAC;SACvE,GAAG,EAAE;WACF,aAAa,UAAU;YACzB;gBAAE,MAAM;gBAAU,OAAO;gBAAe,MAAM,qNAAA,CAAA,YAAS;YAAC;SACzD,GAAG,EAAE;QACN;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACxD,GAAG,EAAE;IAEN,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAMpD,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;gCAQjB,CAAC,qBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;yDAKH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC,qMAAA,CAAA,OAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAM,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;;0EAEV,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAM,KAAK,KAAK;;;;;;;uDALZ,KAAK,IAAI;;;;;8DAQlB,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,6LAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAKzD,wBACC,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;0CAExB,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;wBASjB,CAAC,qBACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAC1B;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAC1B;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,KAAK,KAAK;;;;;;;uCANZ,KAAK,IAAI;;;;;8CASlB,6LAAC;oCACC,SAAS;wCACP;wCACA,UAAU;oCACZ;oCACA,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA9MgB;;QAIC,qIAAA,CAAA,YAAS;;;KAJV", "debugId": null}}]}