(()=>{var e={};e.id=983,e.ids=[983],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1218:(e,r,t)=>{Promise.resolve().then(t.bind(t,9283))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4021:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4941:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(5239),a=t(8088),o=t(8170),l=t.n(o),n=t(893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(r,i);let d={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6696)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\register\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\register\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5789:(e,r,t)=>{Promise.resolve().then(t.bind(t,6696))},6055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6696:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\apd deliary\\\\hyperlocal-delivery\\\\app\\\\auth\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\apd deliary\\hyperlocal-delivery\\app\\auth\\register\\page.tsx","default")},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9283:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(687),a=t(3210),o=t(6189),l=t(5814),n=t.n(l),i=t(6396),d=t(8869),u=t(3931);let c=(0,t(2688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var p=t(4021),m=t(2597),h=t(3861);function x(){let[e,r]=(0,a.useState)({fullName:"",email:"",phone:"",password:"",confirmPassword:"",role:"customer"}),[t,l]=(0,a.useState)(!1),[x,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(!1),[g,v]=(0,a.useState)(""),w=(0,o.useRouter)(),j=(0,i.UU)(),N=t=>{r({...e,[t.target.name]:t.target.value})},k=async r=>{if(r.preventDefault(),b(!0),v(""),e.password!==e.confirmPassword){v("Passwords do not match"),b(!1);return}if(e.password.length<6){v("Password must be at least 6 characters long"),b(!1);return}try{let{data:r,error:t}=await j.auth.signUp({email:e.email,password:e.password,options:{data:{full_name:e.fullName,phone:e.phone,role:e.role}}});if(t)return void v(t.message);if(r.user){let{error:t}=await j.from("users").insert({id:r.user.id,email:e.email,full_name:e.fullName,phone:e.phone,role:e.role});t&&console.error("Profile creation error:",t),"partner"===e.role?w.push("/partner/register"):w.push("/dashboard")}}catch(e){v("An unexpected error occurred")}finally{b(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(n(),{href:"/auth/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:k,children:[g&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:g}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"fullName",name:"fullName",type:"text",required:!0,value:e.fullName,onChange:N,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your full name"}),(0,s.jsx)(d.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:N,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"}),(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,value:e.phone,onChange:N,className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your phone number"}),(0,s.jsx)(c,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,s.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:N,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"customer",children:"Customer"}),(0,s.jsx)("option",{value:"partner",children:"Delivery Partner"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:t?"text":"password",required:!0,value:e.password,onChange:N,className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"}),(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,s.jsx)("button",{type:"button",onClick:()=>l(!t),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:t?(0,s.jsx)(m.A,{className:"h-5 w-5"}):(0,s.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:x?"text":"password",required:!0,value:e.confirmPassword,onChange:N,className:"appearance-none block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm your password"}),(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"}),(0,s.jsx)("button",{type:"button",onClick:()=>f(!x),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:x?(0,s.jsx)(m.A,{className:"h-5 w-5"}):(0,s.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:y,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:y?"Creating account...":"Create account"})})]})})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,699,658,530],()=>t(4941));module.exports=s})();