'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase'
import { 
  BarChart3, 
  Package, 
  Users, 
  Truck, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface DashboardStats {
  totalOrders: number
  totalUsers: number
  totalPartners: number
  totalRevenue: number
  pendingOrders: number
  activeDeliveries: number
  pendingPartnerApplications: number
  deliveredToday: number
}

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null)
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalUsers: 0,
    totalPartners: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    activeDeliveries: 0,
    pendingPartnerApplications: 0,
    deliveredToday: 0
  })
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const checkAdminAccess = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profile?.role !== 'admin') {
        router.push('/dashboard')
        return
      }

      setUser(user)
      await fetchDashboardStats()
      setLoading(false)
    }

    checkAdminAccess()
  }, [supabase, router])

  const fetchDashboardStats = async () => {
    try {
      // Fetch total orders
      const { count: totalOrders } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })

      // Fetch total users
      const { count: totalUsers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'customer')

      // Fetch total partners
      const { count: totalPartners } = await supabase
        .from('delivery_partners')
        .select('*', { count: 'exact', head: true })
        .eq('application_status', 'approved')

      // Fetch total revenue
      const { data: revenueData } = await supabase
        .from('orders')
        .select('total_amount')
        .eq('status', 'delivered')

      const totalRevenue = revenueData?.reduce((sum, order) => sum + order.total_amount, 0) || 0

      // Fetch pending orders
      const { count: pendingOrders } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')

      // Fetch active deliveries
      const { count: activeDeliveries } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .in('status', ['assigned', 'picked_up', 'in_transit'])

      // Fetch pending partner applications
      const { count: pendingPartnerApplications } = await supabase
        .from('delivery_partners')
        .select('*', { count: 'exact', head: true })
        .eq('application_status', 'pending')

      // Fetch delivered today
      const today = new Date().toISOString().split('T')[0]
      const { count: deliveredToday } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'delivered')
        .gte('actual_delivery_time', `${today}T00:00:00`)
        .lt('actual_delivery_time', `${today}T23:59:59`)

      setStats({
        totalOrders: totalOrders || 0,
        totalUsers: totalUsers || 0,
        totalPartners: totalPartners || 0,
        totalRevenue,
        pendingOrders: pendingOrders || 0,
        activeDeliveries: activeDeliveries || 0,
        pendingPartnerApplications: pendingPartnerApplications || 0,
        deliveredToday: deliveredToday || 0
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Monitor and manage your delivery service</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalOrders}</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  All time
                </p>
              </div>
              <Package className="h-12 w-12 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-3xl font-bold text-gray-900">₹{stats.totalRevenue.toLocaleString()}</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <DollarSign className="h-4 w-4 mr-1" />
                  Delivered orders
                </p>
              </div>
              <DollarSign className="h-12 w-12 text-green-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalUsers}</p>
                <p className="text-sm text-blue-600 flex items-center mt-1">
                  <Users className="h-4 w-4 mr-1" />
                  Customers
                </p>
              </div>
              <Users className="h-12 w-12 text-purple-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Partners</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalPartners}</p>
                <p className="text-sm text-orange-600 flex items-center mt-1">
                  <Truck className="h-4 w-4 mr-1" />
                  Approved
                </p>
              </div>
              <Truck className="h-12 w-12 text-orange-600" />
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-yellow-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Orders</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pendingOrders}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Deliveries</p>
                <p className="text-2xl font-bold text-blue-600">{stats.activeDeliveries}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Applications</p>
                <p className="text-2xl font-bold text-red-600">{stats.pendingPartnerApplications}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Delivered Today</p>
                <p className="text-2xl font-bold text-green-600">{stats.deliveredToday}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/admin/orders"
              className="flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
            >
              <Package className="h-6 w-6 text-blue-600 mr-2" />
              <span className="font-semibold text-blue-600">Manage Orders</span>
            </Link>
            
            <Link
              href="/admin/partners"
              className="flex items-center justify-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors"
            >
              <Truck className="h-6 w-6 text-orange-600 mr-2" />
              <span className="font-semibold text-orange-600">Manage Partners</span>
            </Link>
            
            <Link
              href="/admin/users"
              className="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors"
            >
              <Users className="h-6 w-6 text-purple-600 mr-2" />
              <span className="font-semibold text-purple-600">Manage Users</span>
            </Link>
            
            <Link
              href="/admin/analytics"
              className="flex items-center justify-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
            >
              <BarChart3 className="h-6 w-6 text-green-600 mr-2" />
              <span className="font-semibold text-green-600">View Analytics</span>
            </Link>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">System Status</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <span className="font-medium text-green-900">All systems operational</span>
              </div>
              <span className="text-sm text-green-600">Last checked: Just now</span>
            </div>
            
            {stats.pendingPartnerApplications > 0 && (
              <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
                  <span className="font-medium text-yellow-900">
                    {stats.pendingPartnerApplications} partner application(s) need review
                  </span>
                </div>
                <Link
                  href="/admin/partners"
                  className="text-sm text-yellow-600 hover:text-yellow-800 font-medium"
                >
                  Review →
                </Link>
              </div>
            )}
            
            {stats.pendingOrders > 0 && (
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="font-medium text-blue-900">
                    {stats.pendingOrders} order(s) waiting for partner assignment
                  </span>
                </div>
                <Link
                  href="/admin/orders"
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Assign →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
