(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{23:(e,s,t)=>{Promise.resolve().then(t.bind(t,7107))},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3168:(e,s,t)=>{"use strict";t.d(s,{UU:()=>d});var r=t(67),a=t(9509);let l=a.env.NEXT_PUBLIC_SUPABASE_URL,n=a.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;function d(){return(0,r.createBrowserClient)(l,n)}},3999:(e,s,t)=>{"use strict";function r(){let e=Date.now().toString(36).toUpperCase(),s=Math.random().toString(36).substring(2,6).toUpperCase();return"".concat("HLD").concat(e).concat(s)}function a(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e)}function l(e){return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}function n(e,s,t,r){let a=Math.PI/180*(t-e),l=Math.PI/180*(r-s),n=Math.sin(a/2)*Math.sin(a/2)+Math.cos(Math.PI/180*e)*Math.cos(Math.PI/180*t)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n))*6371}function d(e){let s=new Date;return s.setMinutes(s.getMinutes()+(30+5*e)),s}function i(e){switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"assigned":return"bg-blue-100 text-blue-800";case"picked_up":return"bg-purple-100 text-purple-800";case"in_transit":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function c(e){switch(e){case"pending":return"Pending Assignment";case"assigned":return"Assigned to Partner";case"picked_up":return"Package Picked Up";case"in_transit":return"In Transit";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e}}t.d(s,{Iw:()=>c,Q8:()=>d,Yq:()=>l,gd:()=>n,qX:()=>r,qY:()=>i,vv:()=>a})},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7107:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(5155),a=t(2115),l=t(5695),n=t(6874),d=t.n(n),i=t(3168),c=t(3999),o=t(7108),x=t(4186);let u=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var h=t(7924),m=t(4516),g=t(2657);function p(){var e,s;let[t,n]=(0,a.useState)(null),[p,b]=(0,a.useState)([]),[j,y]=(0,a.useState)(!0),[v,f]=(0,a.useState)(""),[N,w]=(0,a.useState)("all"),_=(0,l.useRouter)(),k=(0,i.UU)();(0,a.useEffect)(()=>{(async()=>{let{data:{user:e}}=await k.auth.getUser();if(!e)return _.push("/auth/login");n(e);let{data:s}=await k.from("orders").select("\n          *,\n          pickup_address:addresses!pickup_address_id(address_line_1, city),\n          delivery_address:addresses!delivery_address_id(address_line_1, city)\n        ").eq("customer_id",e.id).order("created_at",{ascending:!1});b(s||[]),y(!1)})()},[k,_]);let A=p.filter(e=>{let s=e.tracking_id.toLowerCase().includes(v.toLowerCase())||e.package_description.toLowerCase().includes(v.toLowerCase()),t="all"===N||e.status===N;return s&&t}),M=(()=>{let e=p.length,s=p.filter(e=>"pending"===e.status).length;return{total:e,pending:s,inTransit:p.filter(e=>["assigned","picked_up","in_transit"].includes(e.status)).length,delivered:p.filter(e=>"delivered"===e.status).length}})();return j?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 max-w-6xl",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",(null==t||null==(e=t.user_metadata)?void 0:e.full_name)||(null==t||null==(s=t.email)?void 0:s.split("@")[0]),"!"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your deliveries and track your packages"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:M.total})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:M.pending})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Transit"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:M.inTransit})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-orange-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Delivered"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:M.delivered})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(d(),{href:"/book",className:"flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors",children:[(0,r.jsx)(u,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:"Book New Delivery"})]}),(0,r.jsxs)(d(),{href:"/track",className:"flex items-center justify-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors",children:[(0,r.jsx)(h.A,{className:"h-6 w-6 text-green-600 mr-2"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:"Track Package"})]}),(0,r.jsxs)(d(),{href:"/addresses",className:"flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-purple-600 mr-2"}),(0,r.jsx)("span",{className:"font-semibold text-purple-600",children:"Manage Addresses"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Your Orders"}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search orders...",value:v,onChange:e=>f(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-2.5"})]}),(0,r.jsxs)("select",{value:N,onChange:e=>w(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"assigned",children:"Assigned"}),(0,r.jsx)("option",{value:"picked_up",children:"Picked Up"}),(0,r.jsx)("option",{value:"in_transit",children:"In Transit"}),(0,r.jsx)("option",{value:"delivered",children:"Delivered"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:0===A.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:0===p.length?"No orders yet":"No orders match your search"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:0===p.length?"Start by booking your first delivery":"Try adjusting your search or filter criteria"}),0===p.length&&(0,r.jsxs)(d(),{href:"/book",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,r.jsx)(u,{className:"h-5 w-5 mr-2"}),"Book Your First Delivery"]})]}):(0,r.jsx)("div",{className:"space-y-4",children:A.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-900",children:["#",e.tracking_id]}),(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-semibold ".concat((0,c.qY)(e.status)),children:(0,c.Iw)(e.status)})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:e.package_description}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1 text-blue-600"}),(0,r.jsxs)("span",{children:["From: ",e.pickup_address.address_line_1,", ",e.pickup_address.city]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1 text-green-600"}),(0,r.jsxs)("span",{children:["To: ",e.delivery_address.address_line_1,", ",e.delivery_address.city]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["Ordered: ",(0,c.Yq)(e.created_at)]}),e.estimated_delivery_time&&(0,r.jsxs)("span",{children:["ETA: ",(0,c.Yq)(e.estimated_delivery_time)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:(0,c.vv)(e.total_amount)})}),(0,r.jsxs)(d(),{href:"/track?id=".concat(e.tracking_id),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Track"]})]})]})},e.id))})})]})]})})}},7108:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[214,874,441,684,358],()=>s(23)),_N_E=e.O()}]);